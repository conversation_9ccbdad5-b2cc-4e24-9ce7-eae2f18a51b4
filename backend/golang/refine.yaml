jobs:

- job: SecurityScan
  steps:
  - task: SnykSecurityScan@1
    name: SnykSAST
    inputs:
      serviceConnectionEndpoint: 'Snyk'
      testType: 'code'
      failOnIssues: true
      monitorWhen: 'always'
      severityThreshold: 'critical'
      codeSeverityThreshold: 'high'
  - task: Gitleaks@3
    name: CredentialsLeak
    inputs:
      scanlocation: '$(Build.SourcesDirectory)'
      configtype: 'predefined'
      predefinedconfigfile: 'GitleaksUdmCombo.toml'
      reportformat: 'sarif'
      verbose: true
      redact: true
      scanmode: 'directory'

- job: QualityChecks
  steps:

  - task: GoTool@0
    name: GoInstall
    inputs:
      version: '1.22.8'

  - task: Bash@3
    name: PerformLints
    env:
      GOLANGCI_VERSION: 'v1.62.2'
    inputs:
      targetType: inline
      script: |
        echo "info: installing golangci $GOLANGCI_VERSION"
        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin $GOLANGCI_VERSION
        export PATH="$(go env GOPATH)/bin:$PATH"

        echo "info: downloading dependencies"
        go mod download -x || true

        echo "info: running build"
        go build ./...

        echo "info: running golangci-lint"
        golangci-lint run -v --timeout 2m

  - task: Bash@3
    name: RunTests
    inputs:
      targetType: inline
      script: |
        echo "info: installing support tools"
        go install github.com/axw/gocov/gocov@latest
        go install github.com/AlekSi/gocov-xml@latest
        go install github.com/jstemmer/go-junit-report@latest
        export PATH="$(go env GOPATH)/bin:$PATH"

        echo "info: running coverage"
        go test -v -coverprofile=coverprofile.out -covermode count $(go list ./... | grep 'domain' | grep -v 'mocks') 2>&1 | go-junit-report > junit-report.xml
        rc=$?

        echo "info: converting to azure format"
        gocov convert coverprofile.out > coverage-report.json
        gocov-xml < coverage-report.json > coverage-report.xml

        exit $rc

  - task: PublishTestResults@2
    name: PublishTests
    condition: always()
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '*junit*.xml'
      searchFolder: '$(Build.SourcesDirectory)'

  - task: PublishCodeCoverageResults@1
    name: PublishCoverage
    condition: always()
    inputs:
      codeCoverageTool: 'Cobertura'
      summaryFileLocation: '$(Build.SourcesDirectory)/coverage-report.xml'

  - task: Bash@3
    name: IntegrationCheck
    env:
      PR_ID: '$(System.PullRequest.PullRequestId)'
      PR_IT: '$(System.PullRequest.PullRequestIteration)'
    inputs:
      targetType: inline
      script: |
        echo "PR_ID: $PR_ID"
        echo "PR_IT: $PR_IT"

        if [[ -f "Dockerfile.test" && -n "$(System.PullRequest.PullRequestId)" ]]; then
          echo "##vso[task.setvariable variable=IntegrationPresent;isOutput=true]true";
          echo "##vso[task.setvariable variable=IterationId;isOutput=true]${PR_IT}";

        else
          echo "##vso[task.setvariable variable=IntegrationPresent;isOutput=true]false"
          echo "##vso[task.setvariable variable=IterationId;isOutput=true]0";
        fi

- job: IntegrationApproval
  dependsOn: QualityChecks
  timeoutInMinutes: 4320 # job times out in 3 days
  pool: server
  condition: ne(variables['System.PullRequest.PullRequestId'], '')
  variables:
    IntegrationPresent: $[ dependencies.QualityChecks.outputs['IntegrationCheck.IntegrationPresent'] ]
    IterationId: $(System.PullRequest.PullRequestIteration)
    PullRequestId: $(System.PullRequest.PullRequestId)
  steps:
  - task: ManualValidation@1
    condition: 'eq(variables.IntegrationPresent, true)'
    timeoutInMinutes: 1440 # task times out in 1 day
    inputs:
      notifyUsers: <EMAIL>
      instructions: ejecutar integraciones?
      onTimeout: reject

  - task: InvokeRESTAPI@1
    condition: 'eq(variables.IntegrationPresent, false)'
    name: IntegrationSkipper
    inputs:
      connectionType: 'connectedServiceName'
      serviceConnection: AzureRESTAPI
      method: POST
      urlSuffix: 'n5/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.ID)/pullRequests/$(PullRequestId)/iterations/$(IterationId)/statuses?api-version=7.1'
      waitForCompletion: 'false'
      headers: |
        {
          "Content-Type":"application/json",
          "Authorization": "Bearer $(system.AccessToken)"
        }
      body: |
        {
          "state": "succeeded",
          "description": "No integrations are declared",
          "context": {
            "genre": "platform-team",
            "name": "integration-check"
          }
        }

- job: IntegrationChecks
  dependsOn: IntegrationApproval
  condition: 'eq(variables.IntegrationPresent, true)'
  steps:
  - task: Bash@3
    name: IntegrationCheck
    inputs:
      targetType: inline
      script: |
        echo "Running integrations"
