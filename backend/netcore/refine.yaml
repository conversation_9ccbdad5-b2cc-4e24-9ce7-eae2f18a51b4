jobs:
- job: SecurityScan
  steps:
  - task: SnykSecurityScan@1
    name: SnykSAST
    inputs:
      serviceConnectionEndpoint: 'Snyk'
      testType: 'code'
      failOnIssues: true
      monitorWhen: 'always'
      severityThreshold: 'critical'
      codeSeverityThreshold: 'high'
  - task: Gitleaks@3
    name: CredentialsLeak
    inputs:
      scanlocation: '$(Build.SourcesDirectory)'
      configtype: 'predefined'
      predefinedconfigfile: 'GitleaksUdmCombo.toml'
      reportformat: 'sarif'
      verbose: true
      redact: true
      # NOTE: organization is not mature enough for full git scans of repos
      scanmode: 'directory'

- job: QualityChecks
  steps:
  - task: UseDotNet@2
    name: DotNetInstall
    inputs:
      version: 8.x
      packageType: sdk

  - task: NuGetAuthenticate@1
    name: NuGetAuth

  - task: DotNetCoreCLI@2
    name: DotNetRestore
    inputs:
      command: 'restore'
      feedsToUse: 'config'
      projects: src
      nugetConfigPath: $(Build.SourcesDirectory)/nuget.config

  - task: DotNetCoreCLI@2
    name: DotNetBuild
    inputs:
      command: publish
      projects: src
      publishWebProjects: ${{ parameters.publishWebProjects }}
      arguments: >-
        /clp:ErrorsOnly
        --runtime $(DotNetRuntime)
        --configuration $(DotNetBuildConfiguration)
        --output $(DotNetOutputDir)

  - task: DotNetCoreCLI@2
    name: DotNetTest
    inputs:
      command: test
      projects: src
      publishTestResults: true
      arguments: >-
        --configuration $(DotNetBuildConfiguration)
        --collect "Code coverage"
        --logger trx
        --runtime $(DotNetRuntime)
        --output $(DotNetOutputDir)
        $(settingsArgument)

  - task: PublishTestResults@2
    name: PublishTestResults
    inputs:
      testRunner: VSTest
      testResultsFiles: '**/*.trx'
      failTaskOnFailedTests: true

  - task: Bash@3
    name: IntegrationCheck
    env:
      PR_ID: '$(System.PullRequest.PullRequestId)'
      PR_IT: '$(System.PullRequest.PullRequestIteration)'
    inputs:
      targetType: inline
      script: |
        echo "PR_ID: $PR_ID"
        echo "PR_IT: $PR_IT"

        if [[ -f "Dockerfile.test" && -n "$(System.PullRequest.PullRequestId)" ]]; then
          echo "##vso[task.setvariable variable=IntegrationPresent;isOutput=true]true";
          echo "##vso[task.setvariable variable=IterationId;isOutput=true]${PR_IT}";

        else
          echo "##vso[task.setvariable variable=IntegrationPresent;isOutput=true]false"
          echo "##vso[task.setvariable variable=IterationId;isOutput=true]0";
        fi

- job: IntegrationApproval
  dependsOn: QualityChecks
  timeoutInMinutes: 4320 # job times out in 3 days
  pool: server
  condition: ne(variables['System.PullRequest.PullRequestId'], '')
  variables:
    IntegrationPresent: $[ dependencies.QualityChecks.outputs['IntegrationCheck.IntegrationPresent'] ]
    IterationId: $(System.PullRequest.PullRequestIteration)
    PullRequestId: $(System.PullRequest.PullRequestId)
  steps:
  - task: ManualValidation@1
    condition: 'eq(variables.IntegrationPresent, true)'
    timeoutInMinutes: 1440 # task times out in 1 day
    inputs:
      notifyUsers: <EMAIL>
      instructions: ejecutar integraciones?
      onTimeout: reject

  - task: InvokeRESTAPI@1
    condition: 'eq(variables.IntegrationPresent, false)'
    name: IntegrationSkipper
    inputs:
      connectionType: 'connectedServiceName'
      serviceConnection: AzureRESTAPI
      method: POST
      urlSuffix: 'n5/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.ID)/pullRequests/$(PullRequestId)/iterations/$(IterationId)/statuses?api-version=7.1'
      waitForCompletion: 'false'
      headers: |
        {
          "Content-Type":"application/json",
          "Authorization": "Bearer $(system.AccessToken)"
        }
      body: |
        {
          "state": "succeeded",
          "description": "No integrations are declared",
          "context": {
            "genre": "platform-team",
            "name": "integration-check"
          }
        }

- job: IntegrationChecks
  dependsOn: IntegrationApproval
  condition: 'eq(variables.IntegrationPresent, true)'
  steps:
  - task: Bash@3
    name: IntegrationCheck
    inputs:
      targetType: inline
      script: |
        echo "Running integrations"
