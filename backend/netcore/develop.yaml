jobs:
- job: Delivery
  variables:
    GIT_VERSION: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/','') }}
    IMAGE_TAG_DEVELOP: 'main-$(Build.SourceVersion)'
  steps:
  - checkout: self
    persistCredentials: true

  - task: Bash@3
    name: InstallDependencies
    inputs:
      targetType: inline
      script: |
        sudo wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq &&\
            sudo chmod +x /usr/bin/yq

  - task: Bash@3
    name: UpdateApplicationSet
    retryCountOnTaskFailure: 3
    env:
      APPSET_REPO: $(AppSetRepo)
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      AZURE_GIT_USERNAME: $(AZURE_GIT_USERNAME)
      AZURE_GIT_PASSWORD: $(AZURE_GIT_PASSWORD)
      NEW_IMAGE: '$(containerRepository)/$(Build.Repository.Name):$(IMAGE_TAG_DEVELOP)'
    inputs:
      targetType: inline
      script: |
        echo "ENVIRONMENT: $(DeliveryEnvironment)"

        git config --global user.email "<EMAIL>"
        git config --global user.name platform-bot
        git clone "https://${AZURE_GIT_USERNAME}:${AZURE_GIT_PASSWORD}@${APPSET_REPO}" "appset-repository"
        cd "appset-repository"
        git checkout main

        for file in $(Build.Repository.Name)/envs/$(DeliveryEnvironment)/*.yaml; do
          if [ -f "$file" ]; then
            echo "Updating: $file"
            yq e '.image.repository = env(NEW_IMAGE)' -i "$file"
          else
            echo "No se encontraron archivos YAML en el directorio especificado."
            exit 1
          fi
        done

        git commit -am "$(Build.Repository.Name): $(Build.SourceVersionMessage)"
        git push origin main

