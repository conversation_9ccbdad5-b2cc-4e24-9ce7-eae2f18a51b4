---
jobs:
    - job: Nuget
      #dependsOn: Scan
      condition: and(succeeded(), eq(variables.isMain, true))
      steps:
      - task: Bash@3
        inputs:
          targetType: input
          script: |
            CSPROJ=$(find *  -type f -name "*.csproj" -print -quit)
            VERSION=$(sed -n 's/.*<Version>\(.*\)<\/Version>.*/\1/p' ""$CSPROJ"")
            if git rev-parse "$VERSION" >/dev/null 2>&1; then
              echo "The $VERSION tag exists in the repository"
              exit 1
            else
                echo "The $VERSION tag does not exist in the repository"
            fi
      - task: UseDotNet@2
        displayName: 'Install .NET Core SDK'
        inputs:
          packageType: 'sdk'
          version: $(dotnet)
          installationPath: '$(Agent.ToolsDirectory)/dotnet'
          includePreviewVersions: false

      - task: NuGetAuthenticate@1

      - task: DotNetCoreCLI@2
        displayName: 'Build'
        inputs:
          command: 'build'
          projects: '**/*.csproj'
          arguments: '--configuration Release'

      - task: DotNetCoreCLI@2
        displayName: 'Pack'
        inputs:
          command: 'pack'
          packagesToPack: '**/*.csproj'
          nobuild: true

      - task: NuGetCommand@2
        displayName: 'Publish $(ProjectName)'
        inputs:
          command: push
          packagesToPush: '$(Build.ArtifactStagingDirectory)/**/*.nupkg'
          nuGetFeedType: 'internal'
          nugetConfigPath: $(Build.SourcesDirectory)/nuget.config
          feedsToUse: 'config'
          allowPackageConflicts: false