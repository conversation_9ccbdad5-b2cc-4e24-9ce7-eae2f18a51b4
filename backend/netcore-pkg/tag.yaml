---
## falta agregar el depend, solo debe correr en main y release
jobs:
    - job: Tag
      dependsOn: Nuget
      steps:
      - checkout: self
        persistCredentials: true
      - task: Bash@3
        inputs:
          targetType: input
          script: | 
            CSPROJ=$(find *  -type f -name "*.csproj" -print -quit)
            VERSION=$(sed -n 's/.*<Version>\(.*\)<\/Version>.*/\1/p' ""$CSPROJ"")
            if git rev-parse "$VERSION" >/dev/null 2>&1; then
              echo "The $VERSION tag exists in the repository"
              exit 1
            else
                echo "The $VERSION tag does not exist in the repository"
                echo $VERSION
                echo "##vso[task.setvariable variable=VERSION;]$VERSION"
            fi
      - task: Bash@3
        inputs:
          targetType: input
          script: | 
            git config --global user.email "$(Build.RequestedForEmail)"
            git config --global user.name "$(Build.RequestedFor)"
            git tag -a "$(VERSION)" -m "automatic tag"
            git push origin "$(VERSION)"