---
jobs:
    - job: Test
      dependsOn: Scan
      condition: and(succeeded(), eq(variables.isOther, true))
      steps:
      - task: UseDotNet@2
        displayName: 'Install .NET Core SDK'
        inputs:
          packageType: 'sdk'
          version: '8.0.x'
          installationPath: '$(Agent.ToolsDirectory)/dotnet'
          includePreviewVersions: false

      - task: NuGetAuthenticate@1

      - task: DotNetCoreCLI@2
        displayName: 'Unit Test'
        inputs:
          command: 'test'
          projects: '**/*.Test.csproj'
          publishTestResults: true