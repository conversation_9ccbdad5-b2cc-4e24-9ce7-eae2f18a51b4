parameters:
- name: publishWebProjects
  default: true

variables:
  - name:  containerRepository
    value: 767397701900.dkr.ecr.us-east-1.amazonaws.com
  - name : isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name : isTag
    value: $[contains(variables['Build.SourceBranch'], 'refs/tags/v')]
  - name: AppSetRepo
    value: dev.azure.com/n5/Platform/_git/ai-applicationset
  - name: DeliveryEnvironment
    ${{ if contains(variables['Build.SourceBranch'], 'refs/tags/v') }}:
      value: production
    ${{ else }}:
      value: develop
  - group: registry-git


stages:
  - stage: refine
    condition: and(eq(variables.isMain, false), eq(variables.isTag, false))
    jobs:
    - template: refine.yaml@templates

  - stage: build
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false))
    jobs:
    - template: build.yaml@templates

  - stage: develop
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false), succeeded('build'))
    dependsOn: build
    variables:
    - group: registry-git
    jobs:
    - template: develop.yaml@templates

  - stage: promote
    condition: eq(variables.isTag, true)
    jobs:
    - template: promote.yaml@templates

  - stage: delivery
    condition: eq(variables.isTag, true)
    dependsOn: promote
    variables:
    - group: registry-git
    jobs:
    - template: delivery.yaml@templates