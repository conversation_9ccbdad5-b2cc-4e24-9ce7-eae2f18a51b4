jobs:
  - job: BuildAndPush
    steps:

    - task: Docker@2
      name: DockerBuild
      inputs:
        repository: '$(containerRepository)/$(Build.Repository.Name)'
        command: 'build'
        arguments: '--build-arg FEED_ACCESSTOKEN=$(SYSTEM_ACCESSTOKEN) --platform linux/amd64'
        Dockerfile: 'Dockerfile'
        tags: 'main-$(Build.SourceVersion)'

    - task: ECRPushImage@1
      inputs:
        awsCredentials: 'AWS ECR Mgmt'
        regionName: 'us-east-1'
        imageSource: 'imagename'
        sourceImageName: '$(containerRepository)/$(Build.Repository.Name)'
        sourceImageTag: 'main-$(Build.SourceVersion)'
        pushTag: 'main-$(Build.SourceVersion)'
        repositoryName: '$(Build.Repository.Name)'
