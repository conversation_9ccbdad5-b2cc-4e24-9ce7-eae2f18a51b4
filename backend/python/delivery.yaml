jobs:
- job: Delivery
  variables:
    IMAGE_TAG_DEVELOP: 'main-$(Build.SourceVersion)'
    IMAGE_TAG_PROMOTED: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/','') }}
    # Extract base name by removing -sudameris/-ccb suffix
    BASE_DIR: $[replace(replace(lower(variables['Build.Repository.Name']), '-sudameris', ''), '-ccb', '')]
    AppSetDir: $(BASE_DIR)/envs
    ${{ if contains(variables['Build.SourceBranch'], 'refs/tags/v') }}:
      IMAGE_TAG: ${{ replace(variables['Build.SourceBranch'], 'refs/tags/','') }}
      ENVIRONMENT: production
    ${{ else }}:
      IMAGE_TAG: main-$(Build.SourceVersion)
      ENVIRONMENT: develop
  steps:
  - checkout: self
    persistCredentials: true

  - task: Bash@3
    name: InstallDependencies
    inputs:
      targetType: inline
      script: |
        sudo wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq &&\
            sudo chmod +x /usr/bin/yq

  - task: Bash@3
    name: UpdateApplicationSet
    retryCountOnTaskFailure: 3
    env:
      APPSET_REPO: $(AppSetRepo)
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      AZURE_GIT_USERNAME: $(AZURE_GIT_USERNAME)
      AZURE_GIT_PASSWORD: $(AZURE_GIT_PASSWORD)
      IMAGE_TAG: $(IMAGE_TAG)
      ENVIRONMENT: $(ENVIRONMENT)
      REPO_NAME: $(Build.Repository.Name)
      APPSET_DIR: $(AppSetDir)
    inputs:
      targetType: inline
      script: |
        echo "IMAGE_TAG: $IMAGE_TAG"
        echo "ENVIRONMENT: $ENVIRONMENT"
        echo "REPO_NAME: $REPO_NAME"
        echo "APPSET_DIR: $APPSET_DIR"

        # Construir la URL completa de la imagen
        IMAGE_URL="$(containerRepository)/${REPO_NAME}:${IMAGE_TAG}"
        echo "IMAGE_URL: $IMAGE_URL"

        git config --global user.email "<EMAIL>"
        git config --global user.name platform-bot
        git clone "https://${AZURE_GIT_USERNAME}:${AZURE_GIT_PASSWORD}@${APPSET_REPO}" "appset-repository"
        cd "appset-repository"
        git checkout main

        # Determinar el archivo correcto según el ambiente y tenant
        if [[ "$ENVIRONMENT" == "develop" ]]; then
          # Development environment
          TARGET_FILE="$APPSET_DIR/develop/develop.yaml"
        elif [[ "$ENVIRONMENT" == "production" ]]; then
          # Determinar si es CCB o Sudameris basado en el nombre del repo
          if [[ "$REPO_NAME" == *"sudameris"* ]] || [[ "$REPO_NAME" == "n5-usage-tracking-service" ]]; then
            # Sudameris prod (incluye n5-usage-tracking-service)
            TARGET_FILE="$APPSET_DIR/production/sudameris-py.yaml"
          else
            # CCB prod
            TARGET_FILE="$APPSET_DIR/production/ccb-pan.yaml"
          fi
        else
          echo "Environment not supported: $ENVIRONMENT"
          exit 1
        fi

        echo "TARGET_FILE: $TARGET_FILE"

        # Actualizar el archivo correspondiente
        if [ -f "$TARGET_FILE" ]; then
          echo "Updating: $TARGET_FILE"
          yq e ".image.repository = \"$IMAGE_URL\"" -i "$TARGET_FILE"
        else
          echo "File not found: $TARGET_FILE"
          exit 1
        fi

        git diff -b || true

        # Mensaje de commit diferente según el entorno
        if [ "$ENVIRONMENT" == "production" ]; then
          if [[ "$REPO_NAME" == *"sudameris"* ]] || [[ "$REPO_NAME" == "n5-usage-tracking-service" ]]; then
            git commit -am "$(Build.Repository.Name): Release $IMAGE_TAG [SUDAMERIS]"
          else
            git commit -am "$(Build.Repository.Name): Release $IMAGE_TAG [CCB]"
          fi
        else
          git commit -am "$(Build.Repository.Name): $(Build.SourceVersionMessage) [DEV]"
        fi

        git push origin main

