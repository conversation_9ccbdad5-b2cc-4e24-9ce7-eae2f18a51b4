---
jobs:
- job: Nuget
  variables:
  - name: NUGET_VERSION
    value: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/v','') }}
  - name: GIT_VERSION
    value: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/','') }}
  steps:
  - task: Bash@3
    name: VersionNameComplianceCheck
    inputs:
      targetType: inline
      script: |
        echo "GIT_VERSION: $GIT_VERSION"
        echo "NUGET_VERSION: $NUGET_VERSION"
        VERSION_REGEX="^[0-9]+\.[0-9]+\.[0-9]+$"

        if [[ $GIT_VERSION != v* ]]; then
          echo '[error] version should respect nomenclature v${VERSION}'
          exit 1
        fi

        if ! [[ ${NUGET_VERSION} =~ ${VERSION_REGEX} ]]; then
            echo "[error] Invalid version format: ${NUGET_VERSION}"
            echo "[error] Version must be in major.minor.patch format (e.g., 1.2.3)"
            exit 1
        fi

  - task: UseDotNet@2
    name: DotNetInstall
    inputs:
      version: 8.x
      packageType: sdk

  - task: NuGetAuthenticate@1
    name: Authenticate

  - task: DotNetCoreCLI@2
    name: Build
    inputs:
      command: 'build'
      projects: '**/*.csproj'
      arguments: >-
        /clp:ErrorsOnly
        --runtime $(DotNetRuntime)
        --configuration $(DotNetBuildConfiguration)

  - task: DotNetCoreCLI@2
    name: Pack
    env:
      PLATFORM_SERVICE_VERSION: '$(NUGET_VERSION)'
    inputs:
      command: 'pack'
      packagesToPack: '**/*.csproj'
      nobuild: false

  - task: NuGetCommand@2
    name: Push
    inputs:
      command: push
      nuGetFeedType: 'internal'
      packagesToPush: '$(Build.ArtifactStagingDirectory)/**/*.nupkg'
      feedsToUse: 'config'
      nugetConfigPath: $(Build.SourcesDirectory)/nuget.config
      #publishPackageMetadata: true
      #allowPackageConflicts: false