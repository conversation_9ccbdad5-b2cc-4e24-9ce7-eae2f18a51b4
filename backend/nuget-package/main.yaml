
parameters:
- name: coverageSettings
  default: ""
- name: publishFeed
  default: $(System.TeamProject)


variables:
  - name:  containerRepository
    value: 767397701900.dkr.ecr.us-east-1.amazonaws.com
  - name : isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name : isTag
    value: $[contains(variables['Build.SourceBranch'], 'refs/tags/v')]
  - name: AppSetRepo
    value: dev.azure.com/n5/Platform/_git/ai-applicationset
  - name: DeliveryEnvironment
    ${{ if contains(variables['Build.SourceBranch'], 'refs/tags/v') }}:
      value: production
    ${{ else }}:
      value: develop
  - name: DotNetBuildConfiguration
    ${{ if contains(variables['Build.SourceBranch'], 'refs/tags/v') }}:
      value: Release
    ${{ else }}:
      value: Debug
  - name: DotNetRuntime
    value: linux-x64
  - name: DotNetOutputDir
    value: out


stages:
  - stage: refine
    condition: eq(variables.isTag, false)
    variables:
    - name: settingsArgument
      ${{ if ne(parameters.coverageSettings, '') }}:
        value: --settings "${{ parameters.coverageSettings }}"
      ${{ else }}:
        value: ''
    jobs:
    - template: refine.yaml@templates

  - stage: publish
    condition: eq(variables.isTag, true)
    jobs:
    - template: publish.yaml@templates
