jobs:
- job: SecurityScan
  steps:
  - task: SnykSecurityScan@1
    name: SnykSAST
    inputs:
      serviceConnectionEndpoint: 'Snyk'
      testType: 'code'
      failOnIssues: true
      monitorWhen: 'always'
      severityThreshold: 'critical'
      codeSeverityThreshold: 'high'
  - task: Gitleaks@3
    name: CredentialsLeak
    inputs:
      scanlocation: '$(Build.SourcesDirectory)'
      configtype: 'predefined'
      predefinedconfigfile: 'GitleaksUdmCombo.toml'
      reportformat: 'sarif'
      verbose: true
      redact: true
      # NOTE: organization is not mature enough for full git scans of repos
      scanmode: 'directory'

- job: QualityChecks
  steps:
  - task: UseDotNet@2
    name: DotNetInstall
    inputs:
      version: 8.x
      packageType: sdk

  - task: NuGetAuthenticate@1
    name: NuGetAuth

  - task: DotNetCoreCLI@2
    name: DotNetRestore
    inputs:
      command: 'restore'
      feedsToUse: 'config'
      projects: src
      nugetConfigPath: $(Build.SourcesDirectory)/nuget.config

  - task: DotNetCoreCLI@2
    name: DotNetBuild
    inputs:
      command: publish
      projects: src
      publishWebProjects: ${{ parameters.publishWebProjects }}
      arguments: >-
        /clp:ErrorsOnly
        --runtime $(DotNetRuntime)
        --configuration $(DotNetBuildConfiguration)
        --output $(DotNetOutputDir)

  - task: DotNetCoreCLI@2
    name: DotNetTest
    inputs:
      command: test
      projects: src
      publishTestResults: true
      arguments: >-
        --configuration $(DotNetBuildConfiguration)
        --collect "Code coverage"
        --logger trx
        --runtime $(DotNetRuntime)
        --output $(DotNetOutputDir)
        $(settingsArgument)

  - task: PublishTestResults@2
    name: PublishTestResults
    inputs:
      testRunner: VSTest
      testResultsFiles: '**/*.trx'
      failTaskOnFailedTests: true
