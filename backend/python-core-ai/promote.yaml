jobs:
- job: Promote
  condition: eq(variables.isTag, true)
  variables:
  - group: registry-git
  - name: GIT_VERSION
    value: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/','') }}
  - name: IMAGE_TAG_DEVELOP
    value: 'main-$(Build.SourceVersion)'
  - name: IMAGE_TAG_PROMOTED
    value: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/','') }}
  steps:
  - task: Bash@3
    name: VersionNameComplianceCheck
    inputs:
      targetType: inline
      script: |
        echo "VERSION: $GIT_VERSION"

        if [[ $GIT_VERSION != v* ]]; then
          echo '[error] version should respect nomenclature v${VERSION}'
          exit 1
        fi

  - task: AWSShellScript@1
    name: PromoteImage
    env:
      REPO_URL: '$(containerRepository)'
      REPO_NAME: $(containerRepoApi)
      SOURCE_TAG: $(IMAGE_TAG_DEVELOP)
      TARGET_TAG: $(IMAGE_TAG_PROMOTED)
    inputs:
      scriptType: inline
      inlineScript: |
        # Si no se especificó un nombre de repositorio, derivarlo del nombre del proyecto
        if [ -z "$REPO_NAME" ]; then
          # Obtener el nombre del repositorio del proyecto actual
          PROJECT_NAME=$(basename $(Build.Repository.Name))
          # Convertir python-core-X-Y a ai-core-X-Y-ccb
          REPO_NAME=$(echo $PROJECT_NAME | sed 's/python-core-/ai-core-/' | sed 's/$/-ccb/')
          echo "REPO_NAME derivado: $REPO_NAME"
        fi
        
        echo "Promoviendo imagen $REPO_URL/$REPO_NAME:$SOURCE_TAG a $REPO_URL/$REPO_NAME:$TARGET_TAG"
        
        # Copiar la imagen con el nuevo tag
        aws ecr get-login-password --region $(AWS.Region) | docker login --username AWS --password-stdin $REPO_URL
        docker pull $REPO_URL/$REPO_NAME:$SOURCE_TAG
        docker tag $REPO_URL/$REPO_NAME:$SOURCE_TAG $REPO_URL/$REPO_NAME:$TARGET_TAG
        docker push $REPO_URL/$REPO_NAME:$TARGET_TAG
