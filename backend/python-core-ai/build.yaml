jobs:
- job: BuildAndPush
  dependsOn: PyProjectMetadata
  variables:
    AWS.AccessKeyID: $(aws_access_key_id)
    AWS.SecretAccessKey: $(aws_secret_access_key)
    AWS.AssumeRoleArn: arn:aws:iam::767397701900:role/IAC-ROLE
    PyProjVersion: $[ dependencies.PyProjectMetadata.outputs['Retrieve.Version'] ]
  steps:
  - task: NuGetAuthenticate@1
    name: NuGetAuth

  - task: Bash@3
    name: CheckVersion
    env:
      REPO_URL: '$(containerRepository)'
      CORE_VERSION: '$(PyProjVersion)'
    inputs:
      targetType: inline
      script: |
        echo "CORE_VERSION: $CORE_VERSION"
        echo "REPO_URL: $REPO_URL"
        mkdir -p ~/.docker
        echo "{\"credHelpers\": {\"${REPO_URL}\": \"ecr-login\"}}" > ~/.docker/config.json

  - task: AWSShellScript@1
    name: BuildPushImage
    env:
      BASE_IMAGE: main-$(Build.SourceVersion)
      IMAGE_TAG: main-$(Build.SourceVersion)
      REPO_NAME: $(containerRepoApi)
      DOCKERFILE: Dockerfile
    inputs:
      scriptType: inline
      inlineScript: |
        # Si no se especificó un nombre de repositorio, derivarlo del nombre del proyecto
        if [ -z "$REPO_NAME" ]; then
          # Obtener el nombre del repositorio del proyecto actual
          PROJECT_NAME=$(basename $(Build.Repository.Name))
          # Convertir python-core-X-Y a ai-core-X-Y-ccb
          REPO_NAME=$(echo $PROJECT_NAME | sed 's/python-core-/ai-core-/' | sed 's/$/-ccb/')
          echo "REPO_NAME derivado: $REPO_NAME"
        fi
        
        # Construir la URL completa de la imagen
        IMAGE_URL="$(containerRepository)/${REPO_NAME}:${IMAGE_TAG}"
        echo "Construyendo imagen: $IMAGE_URL"
        
        # Verificar si existe el Dockerfile especificado, si no, usar Dockerfile por defecto
        if [ ! -f "$DOCKERFILE" ]; then
          DOCKERFILE="Dockerfile"
          echo "Usando Dockerfile por defecto: $DOCKERFILE"
        fi
        
        # Construir y publicar la imagen
        docker build \
          --build-arg FEED_ACCESSTOKEN=$(VSS_NUGET_ACCESSTOKEN) \
          --build-arg BASE_IMAGE \
          --file $DOCKERFILE \
          --progress=plain \
          --tag $IMAGE_URL .
        docker push $IMAGE_URL