jobs:
- job: Develop
  dependsOn: PyProjectMetadata
  variables:
    PyProjVersion: $[ dependencies.PyProjectMetadata.outputs['Retrieve.Version'] ]
  steps:
  - checkout: self
    persistCredentials: true
  - task: Bash@3
    name: InstallDependencies
    inputs:
      targetType: inline
      script: |
        sudo wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq &&\
            sudo chmod +x /usr/bin/yq
  - task: Bash@3
    name: UpdateApplicationSet
    retryCountOnTaskFailure: 3
    env:
      APPSET_REPO: $(AppSetRepo)
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      AZURE_GIT_USERNAME: $(AZURE_GIT_USERNAME)
      AZURE_GIT_PASSWORD: $(AZURE_GIT_PASSWORD)
      IMAGE_TAG: main-$(Build.SourceVersion)
      REPO_NAME: $(containerRepoApi)
      CORE_VERSION: $(PyProjVersion)
      APPSET_DIR: $(AppSetDir)
    inputs:
      targetType: inline
      script: |
        echo "CORE_VERSION: $CORE_VERSION"
        
        # Si no se especificó un nombre de repositorio, derivarlo del nombre del proyecto
        if [ -z "$REPO_NAME" ]; then
          # Obtener el nombre del repositorio del proyecto actual
          PROJECT_NAME=$(basename $(Build.Repository.Name))
          # Convertir python-core-X-Y a ai-core-X-Y-ccb
          REPO_NAME=$(echo $PROJECT_NAME | sed 's/python-core-/ai-core-/' | sed 's/$/-ccb/')
          echo "REPO_NAME derivado: $REPO_NAME"
        fi
        
        # Construir la URL completa de la imagen
        IMAGE_URL="$(containerRepository)/${REPO_NAME}:${IMAGE_TAG}"
        echo "Imagen a actualizar: $IMAGE_URL"
        
        # Si no se especificó un directorio en ApplicationSet, derivarlo del nombre del repositorio
        if [ -z "$APPSET_DIR" ]; then
          # Quitar el sufijo -ccb si existe
          APPSET_DIR=$(echo "$REPO_NAME" | sed 's/-ccb$//')
          echo "APPSET_DIR derivado: $APPSET_DIR"
        fi
        
        echo "Actualizando directorio: $APPSET_DIR"
        
        git config --global user.email <EMAIL>
        git config --global user.name platform-bot
        git clone "https://${AZURE_GIT_USERNAME}:${AZURE_GIT_PASSWORD}@${APPSET_REPO}" "appset-repository"
        cd "appset-repository"
        git checkout main

        # Actualizar los archivos en el entorno de desarrollo
        for file in $APPSET_DIR/envs/dev/*.yaml; do
          if [ -f "$file" ]; then
            echo "Updating: $file"
            yq e ".image.repository = \"$IMAGE_URL\"" -i "$file"
          else
            echo "No se encontraron archivos YAML en el directorio especificado."
            exit 1
          fi
        done

        git diff -b || true
        git commit -am "$(Build.Repository.Name): $(Build.SourceVersionMessage) [DEV]"
        git push origin main