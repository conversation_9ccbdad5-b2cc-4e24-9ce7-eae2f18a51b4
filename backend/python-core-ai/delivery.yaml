parameters:
  - name: environment
    type: string
    default: ''

jobs:
- job: Delivery
  dependsOn: PyProjectMetadata
  variables:
    PyProjVersion: $[ dependencies.PyProjectMetadata.outputs['Retrieve.Version'] ]
    # Extract base name by removing -sudameris/-ccb suffix
    BASE_DIR: $[replace(replace(lower(variables['Build.Repository.Name']), '-sudameris', ''), '-ccb', '')]
    AppSetDir: $(BASE_DIR)/envs
    ${{ if contains(variables['Build.SourceBranch'], 'refs/tags/v') }}:
      IMAGE_TAG: ${{ replace(variables['Build.SourceBranch'], 'refs/tags/','') }}
    ${{ else }}:
      IMAGE_TAG: main-$(Build.SourceVersion)
    ${{ if ne(parameters.environment, '') }}:
      ENVIRONMENT: ${{ parameters.environment }}
    ${{ elseif contains(variables['Build.SourceBranch'], 'refs/tags/v') }}:
      ENVIRONMENT: prod
    ${{ else }}:
      ENVIRONMENT: dev
  steps:
  - checkout: self
    persistCredentials: true
  - task: Bash@3
    name: InstallDependencies
    inputs:
      targetType: inline
      script: |
        sudo wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq &&\
            sudo chmod +x /usr/bin/yq
  - task: Bash@3
    name: UpdateApplicationSet
    retryCountOnTaskFailure: 3
    env:
      APPSET_REPO: $(AppSetRepo)
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      AZURE_GIT_USERNAME: $(AZURE_GIT_USERNAME)
      AZURE_GIT_PASSWORD: $(AZURE_GIT_PASSWORD)
      IMAGE_TAG: $(IMAGE_TAG)
      ENVIRONMENT: $(ENVIRONMENT)
      REPO_NAME: $(containerRepoApi)
      CORE_VERSION: $(PyProjVersion)
      APPSET_DIR: $(AppSetDir)
    inputs:
      targetType: inline
      script: |
        echo "CORE_VERSION: $CORE_VERSION"
        echo "IMAGE_TAG: $IMAGE_TAG"
        echo "ENVIRONMENT: $ENVIRONMENT"
        
        # Si no se especificó un nombre de repositorio, derivarlo del nombre del proyecto
        if [ -z "$REPO_NAME" ]; then
          # Obtener el nombre del repositorio del proyecto actual
          PROJECT_NAME=$(basename $(Build.Repository.Name))
          # Convertir python-core-X-Y a ai-core-X-Y-ccb
          REPO_NAME=$(echo $PROJECT_NAME | sed 's/python-core-/ai-core-/' | sed 's/$/-ccb/')
          echo "REPO_NAME derivado: $REPO_NAME"
        fi
        
        # Construir la URL completa de la imagen
        IMAGE_URL="$(containerRepository)/${REPO_NAME}:${IMAGE_TAG}"
        echo "Imagen a actualizar: $IMAGE_URL"
        
        # Si no se especificó un directorio en ApplicationSet, derivarlo del nombre del repositorio
        if [ -z "$APPSET_DIR" ]; then
          # Quitar el sufijo -ccb si existe
          APPSET_DIR=$(echo "$REPO_NAME" | sed 's/-ccb$//')
          echo "APPSET_DIR derivado: $APPSET_DIR"
        fi
        
        echo "Actualizando directorio: $APPSET_DIR"
        
        git config --global user.email <EMAIL>
        git config --global user.name platform-bot
        git clone "https://${AZURE_GIT_USERNAME}:${AZURE_GIT_PASSWORD}@${APPSET_REPO}" "appset-repository"
        cd "appset-repository"
        git checkout main

        # Determinar el archivo correcto según el ambiente y tenant
        if [[ "$ENVIRONMENT" == "dev" ]]; then
          # Sudameris dev
          TARGET_FILE="$APPSET_DIR/develop/develop.yaml"
        elif [[ "$ENVIRONMENT" == "prod" ]]; then
          # Determinar si es CCB o Sudameris basado en el nombre del repo
          if [[ "$REPO_NAME" == *"sudameris"* ]]; then
            # Sudameris prod
            TARGET_FILE="$APPSET_DIR/production/sudameris-py.yaml"
          else
            # CCB prod
            TARGET_FILE="$APPSET_DIR/production/ccb-pan.yaml"
          fi
        else
          echo "Environment not supported: $ENVIRONMENT"
          exit 1
        fi

        # Actualizar el archivo correspondiente
        if [ -f "$TARGET_FILE" ]; then
          echo "Updating: $TARGET_FILE"
          yq e ".image.repository = \"$IMAGE_URL\"" -i "$TARGET_FILE"
        else
          echo "File not found: $TARGET_FILE"
          exit 1
        fi

        git diff -b || true
        
        # Mensaje de commit diferente según el entorno
        if [ "$ENVIRONMENT" == "prod" ]; then
          git commit -am "$(Build.Repository.Name): Release $IMAGE_TAG"
        elif [ "$ENVIRONMENT" == "sudameris" ]; then
          git commit -am "$(Build.Repository.Name): $(Build.SourceVersionMessage) [SUDAMERIS]"
        else
          git commit -am "$(Build.Repository.Name): $(Build.SourceVersionMessage) [DEV]"
        fi
        
        git push origin main
