jobs:
- job: SecurityScan
  steps:
  - task: SnykSecurityScan@1
    name: SnykSAST
    inputs:
      serviceConnectionEndpoint: 'Snyk'
      testType: 'code'
      failOnIssues: true
      monitorWhen: 'always'
      severityThreshold: 'critical'
      codeSeverityThreshold: 'high'

  - task: Gitleaks@3
    name: CredentialsLeak
    inputs:
      scanlocation: '$(Build.SourcesDirectory)'
      configtype: 'predefined'
      predefinedconfigfile: 'GitleaksUdmCombo.toml'
      reportformat: 'sarif'
      verbose: true
      redact: true
      scanmode: 'directory'
      taskfail: false

- job: QualityChecks
  steps:
  - task: UsePythonVersion@0
    inputs:
      versionSpec: $(PythonVersion)
      architecture: 'x64'
    displayName: 'Install Python $(PythonVersion)'

  - task: Bash@3
    name: PythonTools
    inputs:
      targetType: inline
      script: pip install poetry==1.8.4 ruff==0.8.6 pytest pytest-cov

  - task: PipAuthenticate@1
    inputs:
      artifactFeeds: $(PythonFeed)

  - task: Bash@3
    name: Ruff
    inputs:
      targetType: inline
      script: ruff check src

  - task: Bash@3
    name: Test
    inputs:
      targetType: inline
      script: |
        python -m pytest tests/ --cov=src --cov-report=xml

  - task: PublishCodeCoverageResults@1
    inputs:
      codeCoverageTool: Cobertura
      summaryFileLocation: '$(System.DefaultWorkingDirectory)/coverage.xml'

  - task: Bash@3
    name: IntegrationCheck
    inputs:
      targetType: inline
      script: |
        # Verificar si hay pruebas de integración
        if [ -d "tests/integration" ] && [ "$(find tests/integration -name "test_*.py" | wc -l)" -gt 0 ]; then
          echo "##vso[task.setvariable variable=IntegrationPresent;isOutput=true]true"
          echo "Se encontraron pruebas de integración"
        else
          echo "##vso[task.setvariable variable=IntegrationPresent;isOutput=true]false"
          echo "No se encontraron pruebas de integración"
        fi