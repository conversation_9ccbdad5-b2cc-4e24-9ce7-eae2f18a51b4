variables:
  - name: containerRepository
    value: 767397701900.dkr.ecr.us-east-1.amazonaws.com
  - name: containerRepoRuntime
    value: docker-python-base
  - name: containerRepoApi
    value: $[lower(variables['Build.Repository.Name'])]
  - name: isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name: isTag
    value: $[contains(variables['Build.SourceBranch'], 'refs/tags/v')]
  - name: isCCB
    value: $[contains(variables['Build.Repository.Name'], 'ccb')]
  - name: isSudameris
    value: $[contains(variables['Build.Repository.Name'], 'sudameris')]
  - name: AppSetRepo
    value: dev.azure.com/n5/Platform/_git/ai-applicationset
  - name: PythonFeed
    value: Finsky/Finsky
  - name: PythonVersion
    value: '3.12'
  - name: PythonRuntimeTag
    value: stable
  - name: AWS.Region
    value: us-east-1
  - name: AWS.RoleSessionName
    value: AzurePipeline
  - group: registry-git

stages:
  - stage: refine
    condition: and(eq(variables.isMain, false), eq(variables.isTag, false))
    jobs:
    - template: pyproject.yaml@templates
    - template: refine.yaml@templates

  - stage: build
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false))
    jobs:
    - template: pyproject.yaml@templates
    - template: build.yaml@templates

  - stage: delivery_dev
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false), or(eq(variables.isCCB, false), eq(variables.isSudameris, true)))
    dependsOn: build
    jobs:
    - template: pyproject.yaml@templates
    - template: delivery.yaml@templates
      parameters:
        environment: dev

  - stage: delivery_sudameris
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false), eq(variables.isSudameris, true))
    dependsOn: build
    jobs:
    - template: pyproject.yaml@templates
    - template: delivery.yaml@templates
      parameters:
        environment: sudameris

  - stage: delivery_ccb
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false), eq(variables.isCCB, true))
    dependsOn: build
    jobs:
    - template: pyproject.yaml@templates
    - template: delivery.yaml@templates
      parameters:
        environment: ccb

  # COMMENTED: preflight.yaml template does not exist
  # - stage: preflight
  #   condition: eq(variables.isTag, true)
  #   jobs:
  #   - template: pyproject.yaml@templates
  #   - template: preflight.yaml@templates

  - stage: promote
    condition: eq(variables.isTag, true)
    dependsOn: build  # Changed from preflight to build since preflight is commented
    jobs:
    - template: pyproject.yaml@templates
    - template: promote.yaml@templates

  - stage: tag_delivery
    condition: eq(variables.isTag, true)
    dependsOn: promote
    jobs:
    - template: pyproject.yaml@templates
    - template: delivery.yaml@templates
      parameters:
        environment: prod
