jobs:
- job: PyProjectMetadata
  steps:
  - task: Bash@3
    name: Retrieve
    inputs:
      targetType: inline
      script: |
        echo "[+] Downloading toml & crane cli"
        wget -q 'https://github.com/gnprice/toml-cli/releases/download/v0.2.3/toml-0.2.3-x86_64-linux.tar.gz'

        echo "[+] Validating checksums"
        sha256sum -c <<EOF
        ba12ae6b53fc593a9dcae3d6ef5d50f0382b3f77708603dc237e9145ba7988fc  toml-0.2.3-x86_64-linux.tar.gz
        EOF

        echo "[+] Extract"
        sudo tar xf toml-0.2.3-x86_64-linux.tar.gz -C /usr/bin --strip-components=1
        sudo chmod +rx /usr/bin/toml

        echo "[+] Version"
        PYPROJ_VERSION="$(toml get -r pyproject.toml tool.poetry.version)"
        echo "toml: $(toml --version)"
        echo "pyproj: ${PYPROJ_VERSION}"

        echo "##vso[task.setvariable variable=Version;isOutput=true]${PYPROJ_VERSION}";