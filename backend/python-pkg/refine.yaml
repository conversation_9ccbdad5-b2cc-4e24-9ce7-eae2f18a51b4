jobs:

- job: SecurityScan
  steps:

  - task: SnykSecurityScan@1
    name: SnykSAST
    inputs:
      serviceConnectionEndpoint: 'Snyk'
      testType: 'code'
      failOnIssues: true
      monitorWhen: 'always'
      severityThreshold: 'critical'
      codeSeverityThreshold: 'high'

  - task: Gitleaks@2
    name: CredentialsLeak
    inputs:
      scanlocation: '$(Build.SourcesDirectory)'
      configtype: 'predefined'
      predefinedconfigfile: 'GitleaksUdmCombo.toml'
      reportformat: 'sarif'
      # NOTE: organization is not mature enough for full git scans of repos
      scanmode: 'nogit'

- job: QualityChecks
  steps:

  - task: UsePythonVersion@0
    inputs:
      versionSpec: '3.12' # string. Required. Version spec. Default: 3.x. #cambiar por una variable
      architecture: 'x64' # 'x86' | 'x64'. Required. Architecture. Default: x64.
    displayName: 'use python 3.12'

  - script: pip install poetry ruff pytest pytest-cov
    displayName: 'Install poetry'

  - task: PipAuthenticate@1
    inputs:
      artifactFeeds: <PERSON><PERSON>/<PERSON>sky
  

  - task: Bash@3
    name: pip 
    inputs:
        targetType: inline
        script: |
          poetry export  --without-hashes --format=requirements.txt > requirements.txt
          pip install -r requirements.txt

  - script: ruff check
    displayName: 'Run linter'

  - script: pytest ./tests/unit -v --junitxml=junit/coverage.xml --cov --cov-report=xml --cov-report=html
    displayName: 'Run test'
    continueOnError: False

  - task: PublishTestResults@2
    inputs:
      testResultsFiles: '**/coverage.xml'
      testRunTitle: 'Unit test Python'
    condition: succeededOrFailed()
    displayName: Publish test results



