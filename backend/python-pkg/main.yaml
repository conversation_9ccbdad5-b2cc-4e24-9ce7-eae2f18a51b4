parameters:
- name: publishWebProjects
  default: true

variables:
  - name : isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name : isTag
    value: $[contains(variables['Build.SourceBranch'], 'refs/tags/v')]
  - group: registry-git

stages:
  - stage: refine
    condition: and(eq(variables.isMain, false), eq(variables.isTag, false))
    jobs:
    - template: refine.yaml@templates
  - stage: build-and-publish
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false))
    jobs:
    - template: build.yaml@templates