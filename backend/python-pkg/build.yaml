jobs:
  - job: BuildAndPush
    steps:
      - task: UsePythonVersion@0
        inputs: 
          versionSpec: '3.12'
          architecture: 'x64'
        displayName: 'Use Python $(pythonVersion)'

      - script: |
          pip install build setuptools setuptools_scm wheel twine
        displayName: 'Install pip build'

      - task: Pip<PERSON><PERSON><PERSON>icate@1
        inputs:
          artifactFeeds: <PERSON><PERSON>/<PERSON>

      - script: |
          poetry export --without-hashes --format=requirements.txt > requirements.txt
          pip install -r requirements.txt
        displayName: 'pip install'

      - task: TwineAuthenticate@1
        inputs:
          artifactFeed: Finsky/Finsky
        displayName: 'Twine Authenticate'

      - script: |
          python -m build --wheel --no-isolation --outdir artifacts/
          python -m twine upload -r Finsky --config-file $(PYPIRC_PATH) artifacts/*.whl
        displayName: 'python build and upload to Azure Artifacts'