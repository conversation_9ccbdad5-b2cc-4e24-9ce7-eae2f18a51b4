variables:
  - name:  repository_url
    value: '767397701900.dkr.ecr.us-east-1.amazonaws.com'
  - name : isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name: isOther
    value: $[ne(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name: isDevelop
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/develop')]
  - ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/develop') }}:
      - group: registry-git
      - name: yamlConfig
        value: develop
  - ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/main') }}:
      - group: registry-git
      - name: yamlConfig
        value: main     

stages:

  - stage: sast
    condition: or( eq(variables.isDevelop, true), eq(variables.isMain, true), eq(variables.isOther, true))
    jobs:
      - template: sast.yaml@templates

  - stage: docker
    condition: and ( or( eq(variables.isDevelop, true), eq(variables.isMain, true)), succeeded('sast'))
    dependsOn: sast
    jobs:
      - template: docker.yaml@templates

  - stage: delivery
    condition: and ( or( eq(variables.isDevelop, true), eq(variables.isMain, true)), succeeded('docker'))
    dependsOn: docker
    jobs:
      - template:  delivery.yaml@templates

  - stage: tag
    condition: and ( or( eq(variables.isDevelop, true), eq(variables.isMain, true)), succeeded('delivery'))
    dependsOn: delivery
    jobs:
      - template:  tag-microservice.yaml@templates