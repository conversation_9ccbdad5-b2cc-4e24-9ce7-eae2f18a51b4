jobs:
  - job: Delivery
    steps:
    - checkout: self
      persistCredentials: true
    - task: Bash@3
      inputs:
        targetType: input
        script: |
          VERSION=$(cat Version)
          if [ -z "$VERSION" ]
          then
                echo "The variable is empty,try adding the "version" in your version file"
                exit 1
          fi
          if git rev-parse "$VERSION" >/dev/null 2>&1; then
            echo "The $VERSION tag exists in the repository"
            exit 1
          else
              echo "The $VERSION tag does not exist in the repository"
              echo $VERSION
              echo "##vso[task.setvariable variable=VERSION;]$VERSION"
          fi
    - task: Bash@3
      inputs:
        targetType: input
        script: |
          sudo wget https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 -O /usr/bin/yq &&\
              sudo chmod +x /usr/bin/yq

    - task: Bash@3
      inputs:
        targetType: input
        script: |
          echo $(yamlConfig)
          echo $(variables.yamlConfig)
          echo ${{ variables.yamlConfig }}

          git config --global user.email <EMAIL>
          git config --global user.name platform-bot
          git clone https://${AZURE_GIT_USERNAME}:${AZURE_GIT_PASSWORD}@dev.azure.com/n5/Platform/_git/ai-applicationset "ai-applicationset"
          cd "ai-applicationset"
          git checkout main
          export NEW_REPOSITORY="$REPOSITORY_URL/$(Build.Repository.Name):$VERSION"
          for file in $(Build.Repository.Name)/envs/$(yamlConfig)/*.yaml; do
            if [ -f "$file" ]; then
              echo "Modificando el archivo: $file"
              yq e '.image.repository = env(NEW_REPOSITORY)' -i "$file"
            else
              echo "No se encontraron archivos YAML en el directorio especificado."
            fi
          done
          git commit -am "add new tag docker image"
          git push origin main --force
      env:
        SYSTEM_ACCESSTOKEN: $(System.AccessToken)
        AZURE_GIT_USERNAME: $(AZURE_GIT_USERNAME)
        AZURE_GIT_PASSWORD: $(AZURE_GIT_PASSWORD)