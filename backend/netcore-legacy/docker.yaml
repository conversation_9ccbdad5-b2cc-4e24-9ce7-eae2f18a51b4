jobs:
  - job: Docker
    steps:
    - task: Bash@3
      inputs: 
        targetType: input
        script: |
          VERSION=$(cat Version)
          if [ -z "$VERSION" ]
          then 
                echo "The variable is empty,try adding the "version" in your version file"
                exit 1
          fi 
          if git rev-parse "$VERSION" >/dev/null 2>&1; then
            echo "The $VERSION tag exists in the repository"
            exit 1
          else
              echo "The $VERSION tag does not exist in the repository"
              echo $VERSION
              echo "##vso[task.setvariable variable=VERSION;]$VERSION"
          fi
    - task: NuGetAuthenticate@1
      displayName: 'Authenticate to NuGet'

    - task: Docker@2
      inputs:
        repository: '$(REPOSITORY_URL)/$(Build.Repository.Name)' 
        command: 'build'
        arguments: '--build-arg FEED_ACCESSTOKEN=$(VSS_NUGET_ACCESSTOKEN)'
        Dockerfile: 'Dockerfile'
        tags: '$(VERSION)'

    - task: ECRPushImage@1
      inputs:
        awsCredentials: 'AWS ECR Mgmt'  
        regionName: 'us-east-1'
        imageSource: 'imagename'
        sourceImageName: '$(REPOSITORY_URL)/$(Build.Repository.Name)' 
        sourceImageTag: '$(VERSION)' 
        pushTag: '$(VERSION)' 
        repositoryName: '$(Build.Repository.Name)' 
