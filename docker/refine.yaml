jobs:
- job: SecurityScan
  steps:
  - task: Gitleaks@3
    name: CredentialsLeak
    inputs:
      scanlocation: '$(Build.SourcesDirectory)'
      configtype: 'predefined'
      predefinedconfigfile: 'GitleaksUdmCombo.toml'
      reportformat: 'sarif'
      verbose: true
      redact: true
      # NOTE: organization is not mature enough for full git scans of repos
      scanmode: 'directory'

  - task: Docker@2
    inputs:
      repository: '$(containerRepository)/$(Build.Repository.Name)'
      Dockerfile: 'Dockerfile' # string. Required when command = build || command = buildAndPush. Dockerfile. Default: **/Dockerfile.
      command: 'build'
      tags: 'main-$(Build.SourceVersion)' # string. Optional. Use when command = build || command = push || command = buildAndPush. Tags. Default: $(Build.BuildId).
  - task: trivy@1
    inputs:
      image: '$(containerRepository)/$(Build.Repository.Name):main-$(Build.SourceVersion)'
      loginDockerConfig: false
      severities: 'CRITICAL'
      ignoreUnfixed: true