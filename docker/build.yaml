---
jobs:
    - job: Build
      steps:
      - task: Docker@2
        inputs:
          repository: '$(containerRepository)/$(Build.Repository.Name)'
          Dockerfile: 'Dockerfile' # string. Required when command = build || command = buildAndPush. Dockerfile. Default: **/Dockerfile.
          command: 'build'
          tags: 'main-$(Build.SourceVersion)' # string. Optional. Use when command = build || command = push || command = buildAndPush. Tags. Default: $(Build.BuildId).
      
      - task: ECRPushImage@1
        inputs:
          awsCredentials: 'AWS ECR Mgmt' 
          regionName: 'us-east-1' 
          imageSource: 'imagename'
          sourceImageName: '$(containerRepository)/$(Build.Repository.Name)'
          sourceImageTag: 'main-$(Build.SourceVersion)'
          pushTag: 'main-$(Build.SourceVersion)'
          repositoryName: '$(Build.Repository.Name)'