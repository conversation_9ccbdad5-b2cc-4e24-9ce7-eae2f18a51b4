jobs:
- job: Promote
  condition: eq(variables.isTag, true)
  variables:
  - group: registry-git
  - name: GIT_VERSION
    value: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/','') }}
  - name: IMAGE_TAG_DEVELOP
    value: 'main-$(Build.SourceVersion)'
  - name: IMAGE_TAG_PROMOTED
    value: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/','') }}
  steps:
  - task: Bash@3
    name: VersionNameComplianceCheck
    inputs:
      targetType: input
      script: |
        echo "VERSION: $GIT_VERSION"

        if [[ $GIT_VERSION != v* ]]; then
          echo '[error] version should respect nomenclature v${VERSION}'
          exit 1
        fi

  - task: ECRPullImage@1
    name: VerifyECRImage
    inputs:
      awsCredentials: 'AWS ECR Mgmt'
      regionName: 'us-east-1'
      imageSource: 'imagetag'
      repository: '$(Build.Repository.Name)'
      imageTag: '$(IMAGE_TAG_DEVELOP)'

  - task: Bash@3
    name: PromoteImage
    condition: succeeded()
    inputs:
      targetType: input
      script: |
        sudo apt-get -qq -y update
        sudo apt-get install -qq -y skopeo

        skopeo copy "docker://$(containerRepository)/$(Build.Repository.Name):$(IMAGE_TAG_DEVELOP)" "docker://$(containerRepository)/$(Build.Repository.Name):$(IMAGE_TAG_PROMOTED)"