variables:
- name:  isMain
  value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
- name: isTag
  value: $[contains(variables['Build.SourceBranch'], 'refs/tags/v')]
- name:  containerRepository
  value: 767397701900.dkr.ecr.us-east-1.amazonaws.com


stages:
  - stage: Refine
    condition: and(eq(variables.isMain, false), eq(variables.isTag, false))
    jobs:
    - template: refine.yaml@templates

  - stage: build
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false))
    jobs:
    - template: build.yaml@templates
  
  - stage: promote
    condition: eq(variables.isTag, true)
    jobs:
    - template:  promote.yaml@templates