jobs:
  - job: Tag
    steps:
    - checkout: self
      persistCredentials: true
    - task: Bash@3
      inputs:
        targetType: input
        script: |
          VERSION=$(jq -r '.version' package.json)
          if git rev-parse "$VERSION" >/dev/null 2>&1; then
            echo "The $VERSION tag exists in the repository"
            exit 1
          else
              echo "The $VERSION tag does not exist in the repository"
              echo $VERSION
              echo "##vso[task.setvariable variable=VERSION;]$VERSION"
          fi
      displayName: 'Version validation'

    - task: Bash@3
      inputs:
        targetType: input
        script: | 
          git config --global user.email "$(Build.RequestedForEmail)"
          git config --global user.name "$(Build.RequestedFor)"
          git tag -a "$(VERSION)" -m "automatic tag"
          git push origin "$(VERSION)"
      displayName: 'Creation of the tag version'