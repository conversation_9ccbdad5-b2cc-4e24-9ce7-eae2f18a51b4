jobs:
- job: QualityChecks
  steps:
    - task: UseNode@1
      name: InstallNodeJS
      inputs:
        version: $(NodeVersion)

    - task: Cache@2
      name: CacheNPM
      condition: eq(variables.EnableNPMCache, true)
      inputs:
        key: npm | package-lock.json
        path: /home/<USER>/.npm
        restoreKeys: npm | package-lock.json

    - task: npmAuthenticate@0
      name: Authenticate
      inputs:
        workingFile: .npmrc

    - task: Npm@1
      name: Install
      inputs:
        command: install

    - task: Npm@1
      name: Lint
      inputs:
        command: custom
        customCommand: run lint

    - task: Npm@1
      name: Build
      inputs:
        command: custom
        customCommand: run build

    - task: Npm@1
      name: Test
      condition: eq(variables.EnableUnit, true)
      inputs:
        command: custom
        customCommand: run test:ci

    - task: PublishTestResults@2
      name: PublishTestResults
      condition: succeededOrFailed()
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: '*junit*.xml'
        searchFolder: '$(Build.SourcesDirectory)/reports'

    - task: PublishCodeCoverageResults@2
      name: PublishCoverage
      condition: succeededOrFailed()
      inputs:
        #codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Build.SourcesDirectory)/reports/cobertura*.xml'
- job: SecurityScan
  steps:
  - task: SnykSecurityScan@1
    inputs:
      serviceConnectionEndpoint: 'Snyk'
      testType: 'code'
      failOnIssues: true
      monitorWhen: 'always'
      severityThreshold: 'critical'
      codeSeverityThreshold: 'high'

  - task: Gitleaks@3
    name: CredentialsLeak
    inputs:
      scanlocation: '$(Build.SourcesDirectory)'
      configtype: 'predefined'
      predefinedconfigfile: 'GitleaksUdmCombo.toml'
      reportformat: 'sarif'
      verbose: true
      redact: true
      # NOTE: organization is not mature enough for full git scans of repos
      scanmode: 'directory'