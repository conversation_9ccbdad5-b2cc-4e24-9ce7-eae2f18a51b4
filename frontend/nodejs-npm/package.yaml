jobs:
  - job: Build
    steps:
    - task: Bash@3
      inputs:
        targetType: input
        script: | 
          VERSION=$(jq -r '.version' package.json)
          if git rev-parse "$VERSION" >/dev/null 2>&1; then
            echo "The $VERSION tag exists in the repository"
            exit 1
          else
              echo "The $VERSION tag does not exist in the repository"
          fi
    - task: UseNode@1
      inputs:
        version: $(NodeVersion)
        displayName: 'Install Node.js'
    - task: npmAuthenticate@0 
      inputs:
        workingFile: .npmrc
        # string. Required. .npmrc file to authenticate. 
    - script: |
        npm install
      displayName: 'npm install'
    - script: |
        npm run build
      displayName: 'npm build'
    - task: Npm@1
      inputs:
        command: publish
        publishRegistry: useFeed
        publishFeed: $(feed)