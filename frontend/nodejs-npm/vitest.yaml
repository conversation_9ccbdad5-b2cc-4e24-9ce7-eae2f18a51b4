jobs:
  - job: Test
    steps:
    - task: UseNode@1
      inputs:
        version: $(node)
        displayName: 'Install Node.js'
    - task: npmAuthenticate@0 
      inputs:
        workingFile: .npmrc
        # string. Required. .npmrc file to authenticate. 
    - script: |
        npm install
      displayName: 'npm install'
    - script: |
        npm run test:ci
      displayName: 'npm test'
    - task: PublishTestResults@2
      displayName: 'supply npm test results to pipelines'
      condition: succeededOrFailed() # because otherwise we won't know what tests failed
      inputs:
        testResultsFiles: 'reports/junit.xml'