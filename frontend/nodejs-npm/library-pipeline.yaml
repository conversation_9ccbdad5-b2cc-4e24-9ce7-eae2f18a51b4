variables:
  - name: NodeVersion
    value: '20.12.1'
  - name: feed
    value: 'N5Feed'
  - name : isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/main') }}:
      - group: registry-git
      - name: yamlConfig
        value: main   

  
stages:
  # - stage: vitest
  #   jobs:
  #     - template: vitest.yaml@templates
  
  - stage: refine
    jobs:
      - template: refine.yaml@templates

  - stage: package
    condition: and(eq(variables.isDevAlphaOrMain, true), succeeded('refine'))
    jobs:
      - template: package.yaml@templates

  - stage: tag
    condition: and ( eq(variables.isMain, true), succeeded('package'))
    dependsOn: package
    jobs:
      - template: tag.yaml@templates