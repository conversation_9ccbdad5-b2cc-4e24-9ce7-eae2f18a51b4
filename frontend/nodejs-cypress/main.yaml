variables:
- name: NodeVersion
  value: '20.17.0'
- name: AWSRoleDevelop
  value: arn:aws:iam::211125382625:role/IAC-ROLE
- name: AWSRoleProduction
  value: arn:aws:iam::533267304535:role/IAC-ROLE
- name: AWS.Region
  value: us-east-1
- name: AWS.RoleSessionName
  value: AzurePipelineCypress
- name:  isMain
  value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
- name: isTag
  value: $[contains(variables['Build.SourceBranch'], 'refs/tags/v')]

stages:
  - stage: refine
    condition: and(eq(variables.isMain, false), eq(variables.isTag, false))
    jobs:
    - template: refine.yaml@templates
    variables:
    - group: registry-git

  - stage: test
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false))
    jobs:
    - template: test.yaml@templates
    variables:
    - group: registry-git
    - group: cypress-cloud