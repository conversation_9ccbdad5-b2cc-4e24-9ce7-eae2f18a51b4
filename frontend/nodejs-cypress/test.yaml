
jobs:
- job: CypressPreflight
  variables:
      AWS.AccessKeyID: $(aws_access_key_id)
      AWS.SecretAccessKey: $(aws_secret_access_key)
      AWS.AssumeRoleArn: $(AWSRoleDevelop)
  steps:
  - task: AWSShellScript@1
    name: CreateUser
    env:
      CYPRESS_NEW_USER: $(CYPRE<PERSON>_NEW_USER)
      CYPRESS_NEW_USER_PASSWORD: $(CYPRESS_NEW_USER_PASSWORD)
      COGNITO_POOL_ID: $(COGNITO_POOL_ID)
    inputs:
      scriptType: inline
      inlineScript: |
        echo "CYPRESS_NEW_USER: $CYPRESS_NEW_USER"
        echo "COGNITO_POOL_ID: $COGNITO_POOL_ID"

        aws cognito-idp admin-create-user \
            --user-pool-id "$COGNITO_POOL_ID" \
            --username "$CYPRESS_NEW_USER" \
            --temporary-password "$CYPRESS_NEW_USER_PASSWORD" \
            --message-action SUPPRESS \
            --user-attributes Name=email,Value="$CYPRESS_NEW_USER" Name=email_verified,Value=true Name=custom:role,Value=Administrator

- job: CypressTests
  dependsOn: CypressPreflight
  condition: succeeded()
  steps:
  - task: UseNode@1
    name: InstallNodeJS
    inputs:
      version: $(NodeVersion)

  - task: Cache@2
    name: CacheNPM
    inputs:
      key: npm | package-lock.json
      path: /home/<USER>/.npm
      restoreKeys: npm | package-lock.json

  - task: Cache@2
    name: CacheCypress
    inputs:
      key: cy | package-lock.json
      path: /home/<USER>/.cache/Cypress
      restoreKeys: cy | package-lock.json

  - task: Npm@1
    name: InstallClean
    inputs:
      customCommand: ci --prefer-offline

  - task: Bash@3
    name: CypressInstall
    inputs:
      targetType: inline
      script: npx cypress install

  - task: Bash@3
    name: CypressRun
    env:
      COGNITO_POOL_ID: $(COGNITO_POOL_ID)
      CYPRESS_ADMIN_PASSWORD: $(CYPRESS_ADMIN_PASSWORD)
      CYPRESS_ADMIN_USER: $(CYPRESS_ADMIN_USER)
      CYPRESS_INCORRECT_PASSWORD: $(CYPRESS_INCORRECT_PASSWORD)
      CYPRESS_INVALID_USER: $(CYPRESS_INVALID_USER)
      CYPRESS_LOGIN_URL: $(CYPRESS_LOGIN_URL)
      CYPRESS_NEW_USER_PASSWORD: $(CYPRESS_NEW_USER_PASSWORD)
      CYPRESS_NEW_USER: $(CYPRESS_NEW_USER)
      CYPRESS_PROJECT_ID: $(CYPRESS_PROJECT_ID)
      CYPRESS_RECORD_KEY: $(CYPRESS_RECORD_KEY)
      CYPRESS_VALID_PASSWORD: $(CYPRESS_VALID_PASSWORD)
      CYPRESS_VALID_USER: $(CYPRESS_VALID_USER)
      LANG: $(CYPRESS_LANG)
    inputs:
      targetType: inline
      script: npx cypress run --record

- job: CypressCleanup
  condition: always()
  dependsOn: CypressTests
  variables:
      AWS.AccessKeyID: $(aws_access_key_id)
      AWS.SecretAccessKey: $(aws_secret_access_key)
      AWS.AssumeRoleArn: $(AWSRoleDevelop)
  steps:
  - task: AWSShellScript@1
    name: CleanupUser
    env:
      CYPRESS_NEW_USER: $(CYPRESS_NEW_USER)
      CYPRESS_NEW_USER_PASSWORD: $(CYPRESS_NEW_USER_PASSWORD)
      COGNITO_POOL_ID: $(COGNITO_POOL_ID)
    inputs:
      scriptType: inline
      inlineScript: |
        echo "CYPRESS_NEW_USER: $CYPRESS_NEW_USER"
        echo "COGNITO_POOL_ID: $COGNITO_POOL_ID"

        aws cognito-idp admin-delete-user \
          --user-pool-id "$COGNITO_POOL_ID" \
          --username "$CYPRESS_NEW_USER"