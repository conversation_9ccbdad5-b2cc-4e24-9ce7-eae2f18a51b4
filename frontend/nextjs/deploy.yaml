jobs:
- job: Deploy
  variables:
    AWS.AccessKeyID: $(aws_access_key_id)
    AWS.SecretAccessKey: $(aws_secret_access_key)
  steps:
  - task: Bash@3
    name: SetAWSRole
    inputs:
      targetType: inline
      script: |
        if [ "$(BuildEnvironment)" = "development" ]; then
          AWS_ROLE="$(AWSRoleDevelop)"
        elif [[ "$(CloudFrontDistributionId)" == "$(CloudFrontDistributionIdSudameris)" ]]; then
          AWS_ROLE="$(AWSRoleSudameris)"
        else
          AWS_ROLE="$(AWSRoleProduction)"
        fi
        echo "##vso[task.setvariable variable=AWS.AssumeRoleArn]$AWS_ROLE"
        echo "AWS Role set to: $AWS_ROLE"
  - task: Bash@3
    name: SetArtifactName
    inputs:
      targetType: inline
      script: |
        if [ "$(BuildEnvironment)" = "development" ]; then
          ARTIFACT_NAME="main-$(Build.SourceVersion)"
        else
          ARTIFACT_NAME="v$(PublishVersion)"
        fi
        echo "##vso[task.setvariable variable=ArtifactName]$ARTIFACT_NAME"
        echo "Artifact name set to: $ARTIFACT_NAME"

  - task: DownloadPipelineArtifact@2
    name: RetrieveArtifact
    inputs:
      artifactName: $(ArtifactName)
      targetPath: $(Pipeline.Workspace)

  - task: ExtractFiles@1
    name: ExtractBuild
    inputs:
      archiveFilePatterns: $(LocalTarballPath)
      destinationFolder: $(Pipeline.Workspace)
      cleanDestinationFolder: false
      overwriteExistingFiles: true

  - task: Bash@3
    name: AWSInstall
    inputs:
      targetType: inline
      script: |
        which aws && exit 0
        sudo apt-get update -qq
        sudo apt-get install -qq awscli

  - task: AWSShellScript@1
    name: AWSStoreUpload
    retryCountOnTaskFailure: 5
    inputs:
      scriptType: inline
      inlineScript: |
        echo "Uploading build artifact to S3..."
        aws s3 cp "$(LocalTarballPath)" "$(DestBucketPath)"
        echo "Build artifact uploaded successfully"

  - task: AWSShellScript@1
    name: AWSActiveSync
    retryCountOnTaskFailure: 5
    inputs:
      scriptType: inline
      inlineScript: |
        echo "Syncing Next.js application to S3..."
        
        # For Next.js static export, sync the out directory
        # For server-side rendering, this would be different
        if [ -d "$(LocalDistFolder)/.next/static" ]; then
          echo "Syncing static assets..."
          aws s3 sync "$(LocalDistFolder)/.next/static" "$(BucketProfileFolder)/_next/static" --delete
        fi
        
        if [ -d "$(LocalDistFolder)/public" ]; then
          echo "Syncing public assets..."
          aws s3 sync "$(LocalDistFolder)/public" "$(BucketProfileFolder)" --delete
        fi
        
        # If using static export (next export), sync the out directory
        if [ -d "$(LocalDistFolder)/out" ]; then
          echo "Syncing static export..."
          aws s3 sync "$(LocalDistFolder)/out" "$(BucketProfileFolder)" --delete
        fi
        
        echo "Sync completed successfully"

  - task: AWSShellScript@1
    name: CloudFrontInvalidation
    retryCountOnTaskFailure: 3
    inputs:
      scriptType: inline
      inlineScript: |
        echo "Creating CloudFront invalidation..."
        INVALIDATION_ID=$(aws cloudfront create-invalidation \
          --distribution-id $(CloudFrontDistributionId) \
          --paths "/*" \
          --query "Invalidation.Id" \
          --output text)
        
        echo "Invalidation created with ID: $INVALIDATION_ID"
        
        # Wait for invalidation to complete (optional)
        echo "Waiting for invalidation to complete..."
        aws cloudfront wait invalidation-completed \
          --distribution-id $(CloudFrontDistributionId) \
          --id $INVALIDATION_ID
        
        echo "CloudFront invalidation completed successfully"

  - task: Bash@3
    name: DeploymentSummary
    inputs:
      targetType: inline
      script: |
        echo "=== Deployment Summary ==="
        if [ "$(BuildEnvironment)" = "development" ]; then
          echo "Environment: Development"
          echo "Build: main-$(Build.SourceVersion)"
        else
          echo "Environment: Production"
          echo "Build: v$(PublishVersion)"
        fi
        echo "Repository: $(Build.Repository.Name)"
        echo "S3 Bucket: $(DestBucketPath)"
        echo "CloudFront Distribution: $(CloudFrontDistributionId)"
        echo "Deployment completed at: $(date)"
        echo "=========================="
