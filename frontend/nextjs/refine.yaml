jobs:
- job: QualityChecks
  steps:
    - task: UseNode@1
      name: InstallNodeJS
      inputs:
        version: $(NodeVersion)

    - task: Cache@2
      name: CacheNPM
      condition: eq(variables.EnableNPMCache, true)
      inputs:
        key: npm | package-lock.json
        path: /home/<USER>/.npm
        restoreKeys: npm | package-lock.json

    - task: npmAuthenticate@0
      name: Authenticate
      inputs:
        workingFile: .npmrc

    - task: Npm@1
      name: Install
      inputs:
        command: install

    - task: Bash@3
      name: SetupDevelopmentEnv
      inputs:
        targetType: inline
        script: |
          echo "Setting up development environment for quality checks..."
          
          # Use development environment for linting and testing
          if [ -f ".env.development" ]; then
            cp .env.development .env.local
            echo "Using .env.development for quality checks"
          else
            echo "Warning: .env.development not found, proceeding without environment file"
          fi

    - task: Npm@1
      name: Lint
      inputs:
        command: custom
        customCommand: run lint

    - task: Npm@1
      name: TypeCheck
      continueOnError: true
      inputs:
        command: custom
        customCommand: run type-check
      condition: and(succeeded(), ne(variables['Agent.JobStatus'], 'Failed'))

    - task: Npm@1
      name: Build
      inputs:
        command: custom
        customCommand: run build

    - task: Npm@1
      name: Test
      condition: eq(variables.EnableUnit, true)
      inputs:
        command: custom
        customCommand: run test:ci

    - task: PublishTestResults@2
      name: PublishTestResults
      condition: and(eq(variables.EnableUnit, true), succeededOrFailed())
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: '**/test-results.xml'
        failTaskOnFailedTests: true
        testRunTitle: 'Next.js Unit Tests'

    - task: PublishCodeCoverageResults@1
      name: PublishCoverage
      condition: and(eq(variables.EnableUnit, true), succeededOrFailed())
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '**/coverage/cobertura-coverage.xml'
        reportDirectory: '**/coverage'
        failIfCoverageEmpty: false
