# Next.js Pipeline Template

Este pipeline está diseñado para aplicaciones Next.js que requieren diferentes configuraciones de entorno basadas en archivos `.env`.

## Características

- ✅ Soporte para múltiples entornos (development/production)
- ✅ Manejo automático de archivos `.env.development` y `.env.production`
- ✅ Despliegue automático a S3 + CloudFront
- ✅ Soporte para CCB y Sudameris
- ✅ Verificaciones de calidad (lint, type-check, tests)
- ✅ Aprobación manual para producción
- ✅ Cache de NPM para builds más rápidos

## Estructura de Archivos Requerida

Tu proyecto Next.js debe tener la siguiente estructura:

```
your-nextjs-app/
├── .env.development      # Variables para desarrollo
├── .env.production       # Variables para producción
├── package.json
├── package-lock.json
├── next.config.js        # (opcional)
├── .npmrc               # Para autenticación de paquetes privados
└── azure-pipelines.yml  # Tu archivo de pipeline
```

## Configuración del Pipeline

### 1. Archivo azure-pipelines.yml

```yaml
pool:
  vmImage: ubuntu-22.04

trigger:
  tags:
    include:
    - 'v*'
  branches:
    include:
    - 'main'

resources:
  repositories:
    - repository: templates
      type: git
      name: Platform/n5-microservice-pipelines
      ref: refs/heads/main

extends:
  template: frontend/nextjs/main.yaml@templates
  parameters:
    EnableUnit: true      # Habilitar tests unitarios
    EnableNPMCache: true  # Habilitar cache de NPM
```

### 2. Scripts en package.json

Asegúrate de tener estos scripts en tu `package.json`:

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:ci": "jest --ci --coverage --watchAll=false"
  }
}
```

### 3. Archivos de Entorno

#### `.env.development`
```env
NEXT_PUBLIC_API_URL=https://api-dev.example.com
NEXT_PUBLIC_APP_ENV=development
DATABASE_URL=postgresql://dev-db-url
```

#### `.env.production`
```env
NEXT_PUBLIC_API_URL=https://api.example.com
NEXT_PUBLIC_APP_ENV=production
DATABASE_URL=postgresql://prod-db-url
```

## Flujo del Pipeline

### Para commits en `main` (Desarrollo)

1. **Refine** (solo para branches que no son main/tags)
   - Instala dependencias
   - Ejecuta lint
   - Ejecuta type-check
   - Ejecuta build
   - Ejecuta tests (si están habilitados)

2. **Build Dev**
   - Usa `.env.development`
   - Construye la aplicación
   - Crea artifact con nombre `main-{commit-hash}`

3. **Deploy Dev**
   - Sube a S3 development bucket
   - Invalida CloudFront development
   - Aplicación disponible en entorno de desarrollo

### Para tags `v*` (Producción)

1. **Build Prod**
   - Usa `.env.production`
   - Construye la aplicación
   - Crea artifact con nombre `v{version}`

2. **Promote**
   - Recupera build de desarrollo
   - Requiere aprobación manual
   - Sube a Azure Artifacts
   - Prepara para despliegue

3. **Deploy Prod**
   - Sube a S3 production bucket
   - Invalida CloudFront production
   - Aplicación disponible en producción

4. **Deploy Sudameris** (solo si el repo contiene "sudameris")
   - Despliega automáticamente a entorno Sudameris

## Variables de Entorno del Pipeline

El pipeline utiliza estas variables automáticamente:

- `NodeVersion`: Versión de Node.js (20.12.1)
- `BuildEnvironment`: 'development' o 'production'
- `AwsS3BucketDevelop`: Bucket S3 para desarrollo
- `AwsS3BucketProduction`: Bucket S3 para producción
- `CloudFrontDistributionId*`: IDs de distribuciones CloudFront

## Configuración de AWS

Asegúrate de que los siguientes grupos de variables estén configurados en Azure DevOps:

- `registry-git`: Credenciales para NPM registry
- Variables AWS para cada entorno (develop, production, sudameris)

## Troubleshooting

### Error: "Environment file not found"
- Verifica que `.env.development` y `.env.production` existan en la raíz del proyecto

### Error: "Build failed"
- Revisa que todos los scripts requeridos estén en `package.json`
- Verifica que las dependencias estén correctamente instaladas

### Error: "CloudFront invalidation failed"
- Verifica que las credenciales AWS tengan permisos para CloudFront
- Confirma que el Distribution ID sea correcto

## Ejemplo de Uso

Para usar este pipeline en tu proyecto Next.js:

1. Crea los archivos `.env.development` y `.env.production`
2. Configura los scripts en `package.json`
3. Crea `azure-pipelines.yml` con la configuración mostrada arriba
4. Haz commit a `main` para desplegar a desarrollo
5. Crea un tag `v1.0.0` para desplegar a producción
