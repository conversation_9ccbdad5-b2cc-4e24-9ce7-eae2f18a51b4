parameters:
  - name: EnableUnit
    type: boolean
    default: false
  - name: EnableNPMCache
    type: boolean
    default: true

variables:
- name: NodeVersion
  value: '20.12.1'
- name: isMain
  value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
- name: isTag
  value: $[contains(variables['Build.SourceBranch'], 'refs/tags/v')]
- name: isCCB
  value: $[contains(variables['Build.Repository.Name'], 'ccb')]
- name: isSudameris
  value: $[contains(variables['Build.Repository.Name'], 'sudameris')]
- name: AwsS3BucketDevelop
  value: 'n5-frontend-develop'
- name: AwsS3BucketProduction
  value: 'n5-frontend-production'
- name: AwsS3BucketSudameris
  value: 'n5-frontend-sudameris'
- name: CloudFrontDistributionIdDevelop
  value: 'E2C9KS9HLZF6QB'
- name: CloudFrontDistributionIdProduction
  value: 'E1ISQANI72A4K1'
- name: CloudFrontDistributionIdSudameris
  value: 'E3LNS1HQRC7O4G'
- name: PublishFeed
  value: '<PERSON>sky/<PERSON>sky'

stages:
  - stage: Refine
    condition: and(eq(variables.isMain, false), eq(variables.isTag, false))
    variables:
    - name: EnableUnit
      value: '${{ parameters.EnableUnit }}'
    - name: EnableNPMCache
      value: '${{ parameters.EnableNPMCache }}'
    - group: registry-git
    jobs:
    - template: refine.yaml@templates

  - stage: build_dev
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false))
    variables:
    - name: BuildEnvironment
      value: 'development'
    - group: registry-git
    jobs:
    - template: build.yaml@templates

  - stage: deploy_dev
    condition: and(eq(variables.isMain, true), eq(variables.isTag, false), succeeded('build_dev'))
    dependsOn: build_dev
    variables:
    - name: LocalDistFolder
      value: '$(Pipeline.Workspace)/$(Build.Repository.Name)'
    - name: LocalTarballPath
      value: '$(Pipeline.Workspace)/$(Build.Repository.Name).tar.gz'
    - name: DestBucketPath
      value: s3://$(AwsS3BucketDevelop)/store/$(Build.Repository.Name)/main-$(Build.SourceVersion).tar.gz
    - name: BucketProfileFolder
      value: 's3://$(AwsS3BucketDevelop)/profile/current/$(Build.Repository.Name)'
    - name: CloudFrontDistributionId
      value: $(CloudFrontDistributionIdDevelop)
    - group: registry-git
    jobs:
    - template: deploy.yaml@templates

  - stage: build_prod
    condition: eq(variables.isTag, true)
    variables:
    - name: BuildEnvironment
      value: 'production'
    - name: PublishVersion
      value: ${{ replace(variables['Build.SourceBranch'], 'refs/tags/v','') }}
    - group: registry-git
    jobs:
    - template: build.yaml@templates

  - stage: promote
    condition: eq(variables.isTag, true)
    dependsOn: build_prod
    variables:
    - name: PublishVersion
      value: ${{ replace(variables['Build.SourceBranch'], 'refs/tags/v','') }}
    - name: SourceBucketPath
      value: s3://$(AwsS3BucketDevelop)/store/$(Build.Repository.Name)/main-$(Build.SourceVersion).tar.gz
    - name: LocalTarballPath
      value: '$(Pipeline.Workspace)/$(Build.Repository.Name).tar.gz'
    - group: registry-git
    jobs:
    - template: promote.yaml@templates

  - stage: deploy_prod
    condition: eq(variables.isTag, true)
    dependsOn: promote
    variables:
    - name: PublishVersion
      value: ${{ replace(variables['Build.SourceBranch'], 'refs/tags/v','') }}
    - name: LocalDistFolder
      value: '$(Pipeline.Workspace)/$(Build.Repository.Name)'
    - name: LocalTarballPath
      value: '$(Pipeline.Workspace)/$(Build.Repository.Name).tar.gz'
    - name: DestBucketPath
      value: s3://$(AwsS3BucketProduction)/store/$(Build.Repository.Name)/v$(PublishVersion).tar.gz
    - name: BucketProfileFolder
      value: 's3://$(AwsS3BucketProduction)/profile/current/$(Build.Repository.Name)'
    - name: CloudFrontDistributionId
      value: $(CloudFrontDistributionIdProduction)
    - group: registry-git
    jobs:
    - template: deploy.yaml@templates

  - stage: deploy_sudameris
    condition: and(eq(variables.isTag, true), eq(variables.isSudameris, true))
    dependsOn: promote
    variables:
    - name: PublishVersion
      value: ${{ replace(variables['Build.SourceBranch'], 'refs/tags/v','') }}
    - name: LocalDistFolder
      value: '$(Pipeline.Workspace)/$(Build.Repository.Name)'
    - name: LocalTarballPath
      value: '$(Pipeline.Workspace)/$(Build.Repository.Name).tar.gz'
    - name: DestBucketPath
      value: s3://$(AwsS3BucketSudameris)/store/$(Build.Repository.Name)/v$(PublishVersion).tar.gz
    - name: BucketProfileFolder
      value: 's3://$(AwsS3BucketSudameris)/profile/current/$(Build.Repository.Name)'
    - name: CloudFrontDistributionId
      value: $(CloudFrontDistributionIdSudameris)
    - group: registry-git
    jobs:
    - template: deploy.yaml@templates
