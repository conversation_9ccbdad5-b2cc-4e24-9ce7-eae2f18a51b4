jobs:
- job: Build
  steps:
  - task: UseNode@1
    name: InstallNodeJS
    inputs:
      version: $(NodeVersion)

  - task: npmAuthenticate@0
    name: Authenticate
    inputs:
      workingFile: .npmrc

  - task: Npm@1
    name: Install
    inputs:
      command: install

  - task: Bash@3
    name: SetupEnvironment
    inputs:
      targetType: inline
      script: |
        echo "Setting up environment for: $(BuildEnvironment)"
        
        # Copy the appropriate .env file based on the build environment
        if [ "$(BuildEnvironment)" = "development" ]; then
          if [ -f ".env.development" ]; then
            cp .env.development .env.local
            echo "Using .env.development for development build"
          else
            echo "Warning: .env.development not found"
          fi
        elif [ "$(BuildEnvironment)" = "production" ]; then
          if [ -f ".env.production" ]; then
            cp .env.production .env.local
            echo "Using .env.production for production build"
          else
            echo "Warning: .env.production not found"
          fi
        else
          echo "Unknown build environment: $(BuildEnvironment)"
          exit 1
        fi
        
        # Show environment info (without sensitive values)
        echo "Build Environment: $(BuildEnvironment)"
        echo "Node Version: $(node --version)"
        echo "NPM Version: $(npm --version)"
        
        # List .env files for debugging (without showing content)
        echo "Available .env files:"
        ls -la .env* || echo "No .env files found"

  - task: Npm@1
    name: Build
    inputs:
      command: custom
      customCommand: run build

  - task: Bash@3
    name: CompressDist
    inputs:
      targetType: inline
      script: |
        echo "Compressing Next.js build output..."
        
        # Create directory for the build
        mkdir -p $(Build.Repository.Name)
        
        # Copy Next.js build output (usually .next and public folders)
        if [ -d ".next" ]; then
          cp -r .next $(Build.Repository.Name)/
          echo "Copied .next directory"
        else
          echo "Error: .next directory not found"
          exit 1
        fi
        
        if [ -d "public" ]; then
          cp -r public $(Build.Repository.Name)/
          echo "Copied public directory"
        fi
        
        # Copy package.json and package-lock.json for runtime dependencies
        if [ -f "package.json" ]; then
          cp package.json $(Build.Repository.Name)/
          echo "Copied package.json"
        fi
        
        if [ -f "package-lock.json" ]; then
          cp package-lock.json $(Build.Repository.Name)/
          echo "Copied package-lock.json"
        fi
        
        # Copy next.config.js if it exists
        if [ -f "next.config.js" ]; then
          cp next.config.js $(Build.Repository.Name)/
          echo "Copied next.config.js"
        fi
        
        if [ -f "next.config.mjs" ]; then
          cp next.config.mjs $(Build.Repository.Name)/
          echo "Copied next.config.mjs"
        fi
        
        # Create a simple start script for deployment
        cat > $(Build.Repository.Name)/start.sh << 'EOF'
        #!/bin/bash
        npm install --production
        npm start
        EOF
        chmod +x $(Build.Repository.Name)/start.sh
        
        # Show build size info
        echo "Build contents:"
        du -sh $(Build.Repository.Name)/*
        
        # Create tarball
        tar czf '$(Build.Repository.Name).tar.gz' $(Build.Repository.Name)
        
        echo "Build artifact created: $(Build.Repository.Name).tar.gz"
        ls -lh $(Build.Repository.Name).tar.gz

  - task: Bash@3
    name: SetArtifactName
    inputs:
      targetType: inline
      script: |
        if [ "$(BuildEnvironment)" = "development" ]; then
          ARTIFACT_NAME="main-$(Build.SourceVersion)"
        else
          ARTIFACT_NAME="v$(PublishVersion)"
        fi
        echo "##vso[task.setvariable variable=ArtifactName]$ARTIFACT_NAME"
        echo "Artifact name set to: $ARTIFACT_NAME"

  - task: PublishPipelineArtifact@1
    name: PublishArtifact
    inputs:
      targetPath: $(Build.Repository.Name).tar.gz
      artifactName: $(ArtifactName)
