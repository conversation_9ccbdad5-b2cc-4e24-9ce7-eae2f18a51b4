jobs:
- job: RetrieveFromDevelop
  variables:
    AWS.AccessKeyID: $(aws_access_key_id)
    AWS.SecretAccessKey: $(aws_secret_access_key)
    AWS.AssumeRoleArn: $(AWSRoleDevelop)
  steps:
  - task: AWSShellScript@1
    name: AWSStoreRetrieve
    inputs:
      scriptType: inline
      inlineScript: |
        echo "Retrieving build from development environment..."
        aws s3 cp "$(SourceBucketPath)" "$(LocalTarballPath)"
        echo "Build retrieved successfully from development"

  - task: PublishPipelineArtifact@1
    name: PublishArtifact
    inputs:
      targetPath: $(LocalTarballPath)
      artifactName: v$(PublishVersion)

- job: ProductionManualApproval
  pool: server
  timeoutInMinutes: 4320 # 3 days
  dependsOn: RetrieveFromDevelop
  steps:
  - task: ManualValidation@0
    timeoutInMinutes: 1440 # 1 day
    inputs:
      notifyUsers: <EMAIL>
      instructions: |
        Deploy Next.js application to production?
        
        Repository: $(Build.Repository.Name)
        Version: v$(PublishVersion)
        Source: Development environment
        
        Please review and approve for production deployment.
      onTimeout: 'resume'

- job: PromoteToProduction
  dependsOn: ProductionManualApproval
  variables:
    AWS.AccessKeyID: $(aws_access_key_id)
    AWS.SecretAccessKey: $(aws_secret_access_key)
    AWS.AssumeRoleArn: $(AWSRoleProduction)
  steps:
  - task: DownloadPipelineArtifact@2
    name: RetrieveArtifact
    inputs:
      artifactName: v$(PublishVersion)
      targetPath: $(Pipeline.Workspace)

  - task: Bash@3
    name: AWSInstall
    inputs:
      targetType: inline
      script: |
        which aws && exit 0
        sudo apt-get update -qq
        sudo apt-get install -qq awscli

  - task: UniversalPackages@0
    name: AzureInternalPublish
    inputs:
      command: publish
      vstsFeedPackagePublish: $(Build.Repository.Name)
      publishDirectory: $(LocalTarballPath)
      vstsFeedPublish: $(PublishFeed)
      packagePublishDescription: 'Next.js Production v$(PublishVersion)'
      versionPublish: $(PublishVersion)
      feedsToUsePublish: 'internal'
      versionOption: custom

  - task: AWSShellScript@1
    name: AWSStoreUpload
    retryCountOnTaskFailure: 5
    inputs:
      scriptType: inline
      inlineScript: |
        echo "Uploading production build to S3..."
        aws s3 cp "$(LocalTarballPath)" "$(DestBucketPath)"
        echo "Production build uploaded successfully"

- job: PromoteToSudameris
  condition: eq(variables.isSudameris, true)
  dependsOn: ProductionManualApproval
  variables:
    AWS.AccessKeyID: $(aws_access_key_id)
    AWS.SecretAccessKey: $(aws_secret_access_key)
    AWS.AssumeRoleArn: $(AWSRoleSudameris)
  steps:
  - task: DownloadPipelineArtifact@2
    name: RetrieveArtifact
    inputs:
      artifactName: v$(PublishVersion)
      targetPath: $(Pipeline.Workspace)

  - task: Bash@3
    name: AWSInstall
    inputs:
      targetType: inline
      script: |
        which aws && exit 0
        sudo apt-get update -qq
        sudo apt-get install -qq awscli

  - task: AWSShellScript@1
    name: AWSStoreUpload
    retryCountOnTaskFailure: 5
    inputs:
      scriptType: inline
      inlineScript: |
        echo "Uploading Sudameris build to S3..."
        aws s3 cp "$(LocalTarballPath)" "s3://$(AwsS3BucketSudameris)/store/$(Build.Repository.Name)/v$(PublishVersion).tar.gz"
        echo "Sudameris build uploaded successfully"
