

## Deployment

When writing the artifact for development we upload two copies:
1. the current active deployed code (what cloudfront will serve)
2. the tarball containing the zipped assets

this generally looks like this:

```
s3-bucket
|-> profile/current/{project_name}/{files...}
|-> store/{project_name}/main-{git_commit}.tar.gz
```

The actively deployed code is what is called a "current" profile (this is if we want to implement richer behaviors or deployments schemes). This prefix is worked at cloudfront level and worked at ACL level to avoid exposing the store.

On the other hand, the store contains historic version previously deployed on dev which may be eligible for the promotion pipeline. These may be trimmed periodically by a lambda or a cronjob.

When a version goes live to production that version does indeed get copied to Azure DevOps artifacts.
