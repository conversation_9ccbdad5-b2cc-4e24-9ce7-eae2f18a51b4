jobs:
- job: Publish
  variables:
    AWS.AccessKeyID: $(aws_access_key_id)
    AWS.SecretAccessKey: $(aws_secret_access_key)
    AWS.AssumeRoleArn: $(AWSRoleDevelop)
  steps:
  - task: DownloadPipelineArtifact@2
    name: RetrieveArtifact
    inputs:
      artifactName: main-$(Build.SourceVersion)
      targetPath: $(Pipeline.Workspace)

  - task: ExtractFiles@1
    inputs:
      archiveFilePatterns: $(LocalTarballPath)
      destinationFolder: $(Pipeline.Workspace)
      cleanDestinationFolder: false
      overwriteExistingFiles: true

  - task: Bash@3
    name: AWSInstall
    inputs:
      targetType: inline
      script: |
        which aws && exit 0
        sudo apt-get update -qq
        sudo apt-get install -qq awscli

  - task: AWSShellScript@1
    name: AWSStoreUpload
    retryCountOnTaskFailure: 5
    inputs:
      scriptType: inline
      inlineScript: aws s3 cp "$(LocalTarballPath)" "$(DestBucketPath)"

  - task: AWSShellScript@1
    name: AWSActiveSync
    retryCountOnTaskFailure: 5
    inputs:
      scriptType: inline
      inlineScript: aws s3 sync --delete "$(LocalDistFolder)" "$(BucketProfileFolder)"

  - task: AWSShellScript@1
    name: CloudFrontInvalidation
    retryCountOnTaskFailure: 3
    inputs:
      scriptType: inline
      inlineScript: |
        INVALIDATION_ID=$(aws cloudfront create-invalidation --distribution-id $(CloudFrontDistributionId) --paths "/*" --query "Invalidation.Id" --output text)
        echo "Invalidation created with ID: $INVALIDATION_ID"
