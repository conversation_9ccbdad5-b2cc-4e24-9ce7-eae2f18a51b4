jobs:
- job: RetrieveFromDevelop
  variables:
    AWS.AccessKeyID: $(aws_access_key_id)
    AWS.SecretAccessKey: $(aws_secret_access_key)
    AWS.AssumeRoleArn: $(AWSRoleDevelop)

  steps:
  - task: AWSShellScript@1
    name: AWSStoreRetrieve
    inputs:
      scriptType: inline
      inlineScript: aws s3 cp "$(SourceBucketPath)" "$(LocalTarballPath)"

  - task: PublishPipelineArtifact@1
    name: PublishArtifact
    inputs:
      targetPath: $(LocalTarballPath)
      artifactName: $(PublishVersion)

- job: ProductionManualApproval
  pool: server
  timeoutInMinutes: 4320 # 3d
  dependsOn: RetrieveFromDevelop
  steps:
  - task: ManualValidation@0
    timeoutInMinutes: 1440 # 1d
    inputs:
      notifyUsers: <EMAIL>
      instructions: 'deploy to production?'
      onTimeout: 'resume'

- job: PromoteToProduction
  dependsOn: ProductionManualApproval
  variables:
    AWS.AccessKeyID: $(aws_access_key_id)
    AWS.SecretAccessKey: $(aws_secret_access_key)
    AWS.AssumeRoleArn: $(AWSRoleProduction)
  steps:
  - task: DownloadPipelineArtifact@2
    name: RetrieveArtifact
    inputs:
      artifactName: $(PublishVersion)
      targetPath: $(Pipeline.Workspace)

  - task: Bash@3
    name: AWSInstall
    inputs:
      targetType: inline
      script: |
        which aws && exit 0
        sudo apt-get update -qq
        sudo apt-get install -qq awscli

  - task: UniversalPackages@0
    name: AzureInternalPublish
    inputs:
      command: publish
      vstsFeedPackagePublish: $(Build.Repository.Name)
      publishDirectory: $(LocalTarballPath)
      vstsFeedPublish: $(PublishFeed)
      packagePublishDescription: 'Production v$(PublishVersion)'
      versionPublish: $(PublishVersion)
      feedsToUsePublish: 'internal'
      versionOption: custom

  - task: AWSShellScript@1
    name: AWSStoreUpload
    retryCountOnTaskFailure: 5
    inputs:
      scriptType: inline
      inlineScript: aws s3 cp "$(LocalTarballPath)" "$(DestBucketPath)"

  - task: AWSShellScript@1
    name: AWSActiveSync
    retryCountOnTaskFailure: 5
    inputs:
      scriptType: inline
      inlineScript: |
        tar -xzf "$(LocalTarballPath)" -C "$(Pipeline.Workspace)"
        aws s3 sync --delete "$(LocalDistFolder)/" "$(BucketProfileFolder)"

  - task: AWSShellScript@1
    name: CloudFrontInvalidation
    retryCountOnTaskFailure: 3
    inputs:
      scriptType: inline
      inlineScript: |
        INVALIDATION_ID=$(aws cloudfront create-invalidation --distribution-id $(CloudFrontDistributionId) --paths "/*" --query "Invalidation.Id" --output text)
        echo "Invalidation created with ID: $INVALIDATION_ID"
