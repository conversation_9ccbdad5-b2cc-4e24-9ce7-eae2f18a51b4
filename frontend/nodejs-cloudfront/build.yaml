
jobs:
- job: Build
  steps:
  - task: UseNode@1
    name: InstallNodeJS
    inputs:
      version: $(NodeVersion)

  - task: npmAuthenticate@0
    name: Authenticate
    inputs:
      workingFile: .npmrc

  - task: Npm@1
    name: Install
    inputs:
      command: install

  - task: Npm@1
    name: Build
    inputs:
      command: custom
      customCommand: run build

  - task: Bash@3
    name: CompressDist
    inputs:
      targetType: inline
      script: |
        mkdir $(Build.Repository.Name)
        cp -r dist/*  $(Build.Repository.Name)
        tar cvzf '$(Build.Repository.Name).tar.gz' $(Build.Repository.Name)

  - task: PublishPipelineArtifact@1
    name: PublishArtifact
    inputs:
      targetPath: $(Build.Repository.Name).tar.gz
      artifactName: main-$(Build.SourceVersion)