jobs:
- job: SecurityScan
  steps:
  - task: SnykSecurityScan@1
    inputs:
      serviceConnectionEndpoint: 'Snyk'
      testType: 'code'
      failOnIssues: true
      monitorWhen: 'always'
      severityThreshold: 'critical'
      codeSeverityThreshold: 'high'

  - task: Gitleaks@3
    name: CredentialsLeak
    inputs:
      scanlocation: '$(Build.SourcesDirectory)'
      configtype: 'predefined'
      predefinedconfigfile: 'GitleaksUdmCombo.toml'
      reportformat: 'sarif'
      verbose: true
      redact: true
      # NOTE: organization is not mature enough for full git scans of repos
      scanmode: 'directory'

- job: QualityChecks
  steps:
    - task: UseNode@1
      name: InstallNodeJS
      inputs:
        version: $(NodeVersion)

    - task: Cache@2
      name: CacheNPM
      condition: eq(variables.EnableNPMCache, true)
      inputs:
        key: npm | package-lock.json
        path: /home/<USER>/.npm
        restoreKeys: npm | package-lock.json

    - task: npmAuthenticate@0
      name: Authenticate
      inputs:
        workingFile: .npmrc

    - task: Npm@1
      name: Install
      inputs:
        command: install

    - task: Npm@1
      name: Lint
      inputs:
        command: custom
        customCommand: run lint

    - task: Npm@1
      name: Build
      inputs:
        command: custom
        customCommand: run build

    - task: Npm@1
      name: Test
      condition: eq(variables.EnableUnit, true)
      inputs:
        command: custom
        customCommand: run test:ci

    - task: PublishTestResults@2
      name: PublishTestResults
      condition: succeededOrFailed()
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: '*junit*.xml'
        searchFolder: '$(Build.SourcesDirectory)/reports'

    - task: PublishCodeCoverageResults@2
      name: PublishCoverage
      condition: succeededOrFailed()
      inputs:
        #codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Build.SourcesDirectory)/reports/cobertura*.xml'

    - task: Bash@3
      name: IntegrationCheck
      env:
        PR_ID: '$(System.PullRequest.PullRequestId)'
        PR_IT: '$(System.PullRequest.PullRequestIteration)'
      inputs:
        targetType: inline
        script: |
          echo "PR_ID: $PR_ID"
          echo "PR_IT: $PR_IT"

          if [[ -f "Dockerfile.test" && -n "$(System.PullRequest.PullRequestId)" ]]; then
            echo "##vso[task.setvariable variable=IntegrationPresent;isOutput=true]true";
            echo "##vso[task.setvariable variable=IterationId;isOutput=true]${PR_IT}";

          else
            echo "##vso[task.setvariable variable=IntegrationPresent;isOutput=true]false"
            echo "##vso[task.setvariable variable=IterationId;isOutput=true]0";
          fi

- job: IntegrationApproval
  dependsOn: QualityChecks
  timeoutInMinutes: 4320 # job times out in 3 days
  pool: server
  condition: ne(variables['System.PullRequest.PullRequestId'], '')
  variables:
    IntegrationPresent: $[ dependencies.QualityChecks.outputs['IntegrationCheck.IntegrationPresent'] ]
    IterationId: $(System.PullRequest.PullRequestIteration)
    PullRequestId: $(System.PullRequest.PullRequestId)
  steps:
  - task: ManualValidation@1
    condition: 'eq(variables.IntegrationPresent, true)'
    timeoutInMinutes: 1440 # task times out in 1 day
    inputs:
      notifyUsers: <EMAIL>
      instructions: ejecutar integraciones?
      onTimeout: reject

  - task: InvokeRESTAPI@1
    condition: 'eq(variables.IntegrationPresent, false)'
    name: IntegrationSkipper
    inputs:
      connectionType: 'connectedServiceName'
      serviceConnection: AzureRESTAPI
      method: POST
      urlSuffix: 'n5/$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.ID)/pullRequests/$(PullRequestId)/iterations/$(IterationId)/statuses?api-version=7.1'
      waitForCompletion: 'false'
      headers: |
        {
          "Content-Type":"application/json",
          "Authorization": "Bearer $(system.AccessToken)"
        }
      body: |
        {
          "state": "succeeded",
          "description": "No integrations are declared",
          "context": {
            "genre": "platform-team",
            "name": "integration-check"
          }
        }

- job: IntegrationChecks
  dependsOn: IntegrationApproval
  condition: 'eq(variables.IntegrationPresent, true)'
  steps:
  - task: Bash@3
    name: IntegrationCheck
    inputs:
      targetType: inline
      script: |
        echo "Running integrations"
