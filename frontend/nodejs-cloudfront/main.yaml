parameters:
- name: EnableUnit
  default: false
  type: boolean
- name: EnableNPMCache
  default: false
  type: boolean

variables:
- name: NodeVersion
  value: '20.19.0'
- name: PublishFeed
  value: Fin<PERSON>/<PERSON>sky
- name: AwsS3BucketDevelop
  value: cloudfront-frontend-witty-chicken-development
- name: AwsS3BucketProduction
  value: cloudfront-frontend-eager-lark-production
- name: AwsS3BucketStaging
  value: cloudfront-frontend-pretty-cougar-stg
- name: AwsS3BucketSudameris
  value: front-sudameris-py-prd
- name: AWSRoleDevelop
  value: arn:aws:iam::211125382625:role/IAC-ROLE
- name: AWSRoleProduction
  value: arn:aws:iam::533267304535:role/IAC-ROLE
- name: AWSRoleSudameris
  value: arn:aws:iam::629304253455:role/IAC-ROLE
- name: AWS.Region
  value: us-east-1
- name: AWS.RoleSessionName
  value: AzurePipeline
- name: isMain
  value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
- name: isTag
  value: $[contains(variables['Build.SourceBranch'], 'refs/tags/v')]
- name: isPepAlpha
  value: $[eq(variables['Build.SourceBranch'], 'refs/heads/pep-alpha')]
- name: CloudFrontDistributionIdDevelop
  value: 'E2C9KS9HLZF6QB'
- name: CloudFrontDistributionIdStaging
  value: 'E2C9KS9HLZF6QB'
- name: CloudFrontDistributionIdProduction
  value: 'E1ISQANI72A4K1'
- name: CloudFrontDistributionIdSudameris
  value: 'E3LNS1HQRC7O4G'

stages:
  - ${{ if eq(variables['Build.SourceBranch'], 'refs/heads/pep-alpha') }}:
    - stage: build
      jobs:
      - template: build.yaml@templates

    - stage: deploy_production
      dependsOn: build
      variables:
      - group: registry-git
      - name: LocalDistFolder
        value: '$(Pipeline.Workspace)/$(Build.Repository.Name)'
      - name: LocalTarballPath
        value: '$(Pipeline.Workspace)/$(Build.Repository.Name).tar.gz'
      - name: DestBucketPath
        value: s3://$(AwsS3BucketProduction)/store/$(Build.Repository.Name)/pep-alpha-$(Build.SourceVersion).tar.gz
      - name: BucketProfileFolder
        value: 's3://$(AwsS3BucketProduction)/profile/current/$(Build.Repository.Name)'
      - name: CloudFrontDistributionId
        value: $(CloudFrontDistributionIdProduction)
      jobs:
      - template: pep.yaml@templates

  - ${{ if ne(variables['Build.SourceBranch'], 'refs/heads/pep-alpha') }}:
    - stage: Refine
      condition: and(eq(variables.isMain, false), eq(variables.isTag, false))
      variables:
      - name: EnableUnit
        value: '${{ parameters.EnableUnit }}'
      - name: EnableNPMCache
        value: '${{ parameters.EnableNPMCache }}'
      jobs:
      - template: refine.yaml@templates

    - stage: build
      condition: and(eq(variables.isMain, true), eq(variables.isTag, false))
      jobs:
      - template: build.yaml@templates

    - stage: develop
      condition: and(eq(variables.isMain, true), eq(variables.isTag, false), succeeded('build'))
      dependsOn: build
      variables:
      - name: LocalDistFolder
        value: '$(Pipeline.Workspace)/$(Build.Repository.Name)'
      - name: LocalTarballPath
        value: '$(Pipeline.Workspace)/$(Build.Repository.Name).tar.gz'
      - name: DestBucketPath
        value: 's3://$(AwsS3BucketDevelop)/store/$(Build.Repository.Name)/main-$(Build.SourceVersion).tar.gz'
      - name: BucketProfileFolder
        value: 's3://$(AwsS3BucketDevelop)/profile/current/$(Build.Repository.Name)'
      - name: CloudFrontDistributionId
        value: $(CloudFrontDistributionIdDevelop)
      - group: registry-git
      jobs:
      - template:  develop.yaml@templates

    - stage: staging
      condition: and(eq(variables.isMain, true), eq(variables.isTag, false), succeeded('build'))
      dependsOn: develop
      variables:
      - name: LocalDistFolder
        value: '$(Pipeline.Workspace)/$(Build.Repository.Name)'
      - name: LocalTarballPath
        value: '$(Pipeline.Workspace)/$(Build.Repository.Name).tar.gz'
      - name: SourceBucketPath
        value: 's3://$(AwsS3BucketDevelop)/store/$(Build.Repository.Name)/main-$(Build.SourceVersion).tar.gz'
      - name: DestBucketPath
        value: 's3://$(AwsS3BucketStaging)/store/$(Build.Repository.Name)/main-$(Build.SourceVersion).tar.gz'
      - name: BucketProfileFolder
        value: 's3://$(AwsS3BucketStaging)/profile/current/$(Build.Repository.Name)'
      - name: CloudFrontDistributionId
        value: $(CloudFrontDistributionIdStaging)
      - group: registry-git
      jobs:
      - template:  staging.yaml@templates

    - stage: promote
      condition: eq(variables.isTag, true)
      dependsOn: develop
      variables:
      - group: registry-git
      - name: PublishVersion
        value: ${{ replace( variables['Build.SourceBranch'], 'refs/tags/v','') }}
      - name: LocalTarballPath
        value: '$(Pipeline.Workspace)/$(Build.Repository.Name).tar.gz'
      - name: LocalDistFolder
        value: '$(Pipeline.Workspace)/$(Build.Repository.Name)'
      - name: SourceBucketPath
        value: 's3://$(AwsS3BucketDevelop)/store/$(Build.Repository.Name)/main-$(Build.SourceVersion).tar.gz'
      - name: DestBucketPath
        value: s3://$(AwsS3BucketProduction)/store/$(Build.Repository.Name)/v$(PublishVersion).tar.gz
      - name: BucketProfileFolder
        value: 's3://$(AwsS3BucketProduction)/profile/current/$(Build.Repository.Name)'
      - name: CloudFrontDistributionId
        value: $(CloudFrontDistributionIdProduction)
      jobs:
      - job: ProductionManualApproval
        pool: server
        timeoutInMinutes: 4320 # 3d
        steps:
        - task: ManualValidation@0
          timeoutInMinutes: 1440 # 1d
          inputs:
            notifyUsers: <EMAIL>
            instructions: 'Deploy to Production (CCB) and Sudameris?'
            onTimeout: 'resume'

      - job: RetrieveFromDevelop
        dependsOn: ProductionManualApproval
        variables:
          AWS.AccessKeyID: $(aws_access_key_id)
          AWS.SecretAccessKey: $(aws_secret_access_key)
          AWS.AssumeRoleArn: $(AWSRoleDevelop)
        steps:
        - task: AWSShellScript@1
          name: AWSStoreRetrieve
          inputs:
            scriptType: inline
            inlineScript: aws s3 cp "$(SourceBucketPath)" "$(LocalTarballPath)"

        - task: PublishPipelineArtifact@1
          name: PublishArtifact
          inputs:
            targetPath: $(LocalTarballPath)
            artifactName: $(PublishVersion)

      - job: PromoteToSudameris
        dependsOn: RetrieveFromDevelop
        variables:
          AWS.AccessKeyID: $(aws_access_key_id)
          AWS.SecretAccessKey: $(aws_secret_access_key)
          AWS.AssumeRoleArn: $(AWSRoleSudameris)
        steps:
        - task: DownloadPipelineArtifact@2
          name: RetrieveArtifact
          inputs:
            artifactName: $(PublishVersion)
            targetPath: $(Pipeline.Workspace)

        - task: Bash@3
          name: AWSInstall
          inputs:
            targetType: inline
            script: |
              which aws && exit 0
              sudo apt-get update -qq
              sudo apt-get install -qq awscli

        - task: AWSShellScript@1
          name: AWSStoreUpload
          retryCountOnTaskFailure: 5
          inputs:
            scriptType: inline
            inlineScript: aws s3 cp "$(LocalTarballPath)" "s3://$(AwsS3BucketSudameris)/store/$(Build.Repository.Name)/v$(PublishVersion).tar.gz"

        - task: AWSShellScript@1
          name: AWSActiveSync
          retryCountOnTaskFailure: 5
          inputs:
            scriptType: inline
            inlineScript: |
              tar -xzf "$(LocalTarballPath)" -C "$(Pipeline.Workspace)"
              aws s3 sync --delete "$(LocalDistFolder)/" "s3://$(AwsS3BucketSudameris)/profile/current/$(Build.Repository.Name)"

        - task: AWSShellScript@1
          name: CloudFrontInvalidation
          retryCountOnTaskFailure: 3
          inputs:
            scriptType: inline
            inlineScript: |
              INVALIDATION_ID=$(aws cloudfront create-invalidation --distribution-id $(CloudFrontDistributionIdSudameris) --paths "/*" --query "Invalidation.Id" --output text)
              echo "Invalidation created with ID: $INVALIDATION_ID"
