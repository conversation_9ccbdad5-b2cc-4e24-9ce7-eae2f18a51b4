# Changelog

All notable changes to this project will be documented in this file.

## [0.4.0] - 2024-12-19

### 🚀 Features

- New pipeline for docker images

## [0.3.0] - 2024-12-11

### 🚀 Features

- New frontend nodejs-npm pipeline
- New .net legacy pipeline for backend
- New netcore-pkg pipeline for backend
- Python backend pipeline
- Add golang pipeline
- Allow to set a coverage settings for .NET backend pipeline

### ⚙️ Miscellaneous Tasks

- Set new applicationset repo for netcore
- Update docs and tag release

## [0.2.1] - 2024-10-17

### 🐛 Bug Fixes

- Add manual approval to production deployment on nodejs-cloudfront pipeline

### ⚙️ Miscellaneous Tasks

- Update AwsS3BucketProduction with final value
- Update CHANGELOG and tag release

## [0.2.0] - 2024-10-16

### 🚀 Features

- Add new nodejs-cloudfront for frontend

### 🐛 Bug Fixes

- Change from dotnet build to dotnet publish to fix n5-ai.
- Make publishWebProjects a parameter with default=true as console projects fail

### ⚙️ Miscellaneous Tasks

- Add CHANGELOG for v0.2.0 and tag release

## [0.1.0] - 2024-10-16

### 🚀 Features

- Add .NET core pipeline for backend

### ⚙️ Miscellaneous Tasks

- Add CHANGELOG for v0.1.0 and tag release

<!-- generated by git-cliff -->
